# 研究结果数据结构设计

## 概述

为了支持流式输出中的多阶段思考过程，我们设计了一个新的数据结构来处理 `research_result` 数据。这个结构能够：

1. 按 `thought_number` 分组不同的思考阶段
2. 在每个思考编号下存储多个 `stage`（阶段）的数据
3. 支持实时更新和展示当前进度
4. 兼容现有的思维链组件

## 数据结构

### 流式输入数据格式

```typescript
interface StreamData {
  conversation_id: string;
  message_id: string;
  task_id: string;
  research_result: {
    stage: string;           // 当前阶段：analysis, rewriting, execution, synthesis
    thought_number: number;  // 思考编号：1, 2, 3, ...
    next_step_needed: boolean;
    // 根据不同阶段包含不同的数据
    deep_analysis_result?: any;
    required_tools?: any[];
    // ... 其他阶段特定数据
  };
}
```

### 处理后的数据结构

```typescript
interface ThoughtChainData {
  stages: Record<number, Record<string, ResearchStage>>;  // thought_number -> stage -> data
  currentStage: string;
  currentThoughtNumber: number;
  processedThoughts: ProcessedThought[];
}

interface ProcessedThought {
  thought_number: number;
  stages: Record<string, ResearchStage>;
  currentStage: string;
  thought_purpose: string;
  thought: string;
  next_step_needed: boolean;
  required_tools?: any[];
  status: 'pending' | 'thinking' | 'completed';
}
```

## 使用示例

### 1. 流式数据处理

当接收到流式数据时，系统会自动处理：

```typescript
// 在 updateStreamMessage 中
if (message.research_result) {
  const existingData = lastMessage.research_result?.stages ? lastMessage.research_result : null;
  const processedData = processResearchResult(existingData, message.research_result);
  lastMessage.research_result = processedData;
  lastMessage.thoughts = processedData.processedThoughts;
}
```

### 2. 组件中使用

在思维链组件中，数据会被自动转换为适合展示的格式：

```typescript
// 在 answer.tsx 中
const realThoughtSteps = item.thoughts?.map((thought: any, index: number) => {
  // 支持多阶段数据
  let currentStageData = thought;
  if (thought.stages && thought.currentStage) {
    currentStageData = thought.stages[thought.currentStage];
  }
  
  return {
    id: `step-${thought.thought_number}`,
    title: thought.thought_purpose,
    content: thought.thought,
    status: thought.status,
    // ... 其他属性
  };
});
```

## 阶段类型说明

### analysis（深度分析）
- 分析用户查询的意图和需求
- 提取关键洞察和分析要点
- 数据字段：`deep_analysis_result.analysis_findings.key_insights`

### rewriting（查询重写）
- 将用户查询转换为具体的工具调用
- 生成需要执行的工具列表
- 数据字段：`required_tools`

### execution（执行查询）
- 执行具体的数据查询或工具调用
- 收集执行结果
- 数据字段：`tool_executions`

### synthesis（结果综合）
- 综合分析所有执行结果
- 生成最终答案
- 数据字段：待定

## 数据流程

```
流式输入 -> processResearchResult() -> ThoughtChainData -> 组件展示
    |                                        |
    v                                        v
research_result                    processedThoughts[]
```

## 兼容性

新的数据结构完全兼容现有的思维链组件：

1. 保持 `thoughts` 字段的数组格式
2. 每个思考项包含必要的展示字段
3. 支持渐进式升级，旧数据仍可正常工作

## 调试支持

为了便于调试，处理后的数据会保留原始信息：

```typescript
{
  // 展示用的处理后数据
  thought_purpose: '深度分析',
  thought: '分析内容...',
  
  // 调试用的原始数据
  rawThought: { /* 原始思考数据 */ },
  rawStageData: { /* 当前阶段原始数据 */ }
}
```

## 扩展性

这个设计支持未来添加新的阶段类型：

1. 在 `getStageDisplayName()` 中添加新阶段的显示名称
2. 在 `generateThoughtContent()` 中添加新阶段的内容生成逻辑
3. 在 `determineThoughtStatus()` 中添加新阶段的状态判断逻辑

## 性能考虑

1. 使用 `timestamp` 字段来确定最新的阶段数据
2. 避免重复处理相同的数据
3. 只在数据变化时更新组件状态
