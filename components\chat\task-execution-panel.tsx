export type ResearchStage =
  | "planning"
  | "rewriting"
  | "execution"
  | "analysis"
  | string;

export interface TaskRecord {
  conversation_id: string;
  message_id: string;
  task_id: string;
  research_result: any;
}

export interface TaskExecutionPanelProps {
  data: Record<string, TaskRecord>;
  defaultStage?: ResearchStage;
}

function groupByStage(data: Record<string, TaskRecord>) {
  const stages: Record<string, TaskRecord[]> = {}
  Object.values(data || {}).forEach((item) => {
    const stage = item?.research_result?.stage || "unknown"
    if (!stages[stage]) stages[stage] = []
    stages[stage].push(item)
  })
  // 每个阶段按 thought_number 升序
  Object.keys(stages).forEach((k) => {
    stages[k].sort(
      (a, b) => (a?.research_result?.thought_number ?? 0) - (b?.research_result?.thought_number ?? 0)
    )
  })
  return stages
}