# 新版研究思维链实现

## 概述

基于你提供的 `planMockData` 数据结构，我重新设计了思维链处理系统，完全替代了旧版本的兼容性处理。新系统专门针对研究流程的多阶段思考进行了优化。

## 核心组件

### 1. 数据处理器 (`utils/thoughtChainProcessor.ts`)

**核心接口:**
```typescript
interface ResearchResult {
  stage: 'planning' | 'rewriting' | 'execution' | 'analysis';
  thought_number: number;
  next_step_needed: boolean;
  research_plan?: any;      // planning 阶段数据
  required_tools?: any[];   // rewriting 阶段数据
  tool_executions?: any[];  // execution 阶段数据
  deep_analysis_result?: any; // analysis 阶段数据
}

interface ProcessedThought {
  thought_number: number;
  stages: Record<string, ResearchResult>;
  currentStage: string;
  title: string;
  content: string;
  next_step_needed: boolean;
  status: 'pending' | 'thinking' | 'completed';
  stageData: {
    planning?: any;
    rewriting?: any;
    execution?: any;
    analysis?: any;
  };
}
```

**主要功能:**
- `processResearchResult()`: 处理流式输入数据
- `generateProcessedThoughts()`: 生成处理后的思考数据
- `convertToThoughtSteps()`: 转换为组件格式

### 2. 新版思维链组件 (`components/chat/ResearchThoughtChain.tsx`)

**特性:**
- 支持四种阶段类型：planning, rewriting, execution, analysis
- 标签页式阶段切换界面
- 每个阶段有专门的内容渲染逻辑
- 实时状态更新和进度显示
- 响应式设计

**阶段特定渲染:**
- **Planning**: 显示研究目标和工具任务分解
- **Rewriting**: 显示需要执行的工具列表和查询内容
- **Execution**: 显示工具执行结果和状态
- **Analysis**: 显示关键洞察和建议

### 3. Store 集成 (`store/useConversion.ts`)

**更新内容:**
- 新增 `research_result` 字段支持
- 使用 `processResearchResult()` 处理流式数据
- 自动生成 `processedThoughts` 数组

### 4. Answer 组件集成 (`components/chat/answer.tsx`)

**更新逻辑:**
```typescript
// 检测新版数据
const hasResearchResult = item.research_result && 
  item.research_result.processedThoughts && 
  item.research_result.processedThoughts.length > 0;

// 优先使用新版组件
{hasResearchResult && (
  <ResearchThoughtChain
    thoughts={item.research_result.processedThoughts}
    isResponding={item.isThinking && isLast}
    messageId={item.id}
  />
)}

// 兼容旧版数据
{hasThoughts && !hasResearchResult && (
  <ThoughtChain ... />
)}
```

## 数据流程

```
planMockData 格式
      ↓
processResearchResult()
      ↓
ThoughtChainData
      ↓
ResearchThoughtChain 组件
      ↓
用户界面展示
```

## 使用示例

### 流式数据处理
```typescript
// 在 updateStreamMessage 中
if (message.research_result) {
  const existingData = lastMessage.research_result?.stages ? 
    lastMessage.research_result : null;
  const processedData = processResearchResult(existingData, message.research_result);
  lastMessage.research_result = processedData;
  lastMessage.thoughts = processedData.processedThoughts;
}
```

### 组件使用
```typescript
<ResearchThoughtChain
  thoughts={processedThoughts}
  isResponding={isThinking}
  messageId={messageId}
/>
```

## 演示和测试

### 1. 演示页面
- 路径: `/pages/demo/research-thought-chain.tsx`
- 功能: 模拟流式数据接收，展示组件功能
- 包含控制按钮和状态显示

### 2. 测试文件
- 路径: `/utils/__tests__/thoughtChainProcessor.test.ts`
- 覆盖: 数据处理、多阶段处理、状态转换等

## 支持的数据格式

### Planning 阶段
```json
{
  "stage": "planning",
  "thought_number": 1,
  "research_plan": {
    "overall_goal": "研究目标",
    "tool_specific_tasks": {
      "nl2sql": {
        "key_business_metrics": ["指标1", "指标2"]
      }
    }
  }
}
```

### Rewriting 阶段
```json
{
  "stage": "rewriting",
  "thought_number": 1,
  "required_tools": [
    {
      "tool_name": "nl2sql",
      "parameters": {
        "query": "SQL查询语句"
      }
    }
  ]
}
```

### Execution 阶段
```json
{
  "stage": "execution",
  "thought_number": 1,
  "tool_executions": [
    {
      "tool_name": "nl2sql",
      "query": "执行的查询",
      "result": { "data": "结果" }
    }
  ]
}
```

### Analysis 阶段
```json
{
  "stage": "analysis",
  "thought_number": 1,
  "deep_analysis_result": {
    "analysis_findings": {
      "key_insights": [
        {
          "insight": "关键洞察",
          "confidence": "高"
        }
      ]
    },
    "recommendations": [
      {
        "type": "建议类型",
        "action": "具体行动",
        "priority": "优先级"
      }
    ]
  }
}
```

## 特点

1. **完全重写**: 不再兼容旧版思维链格式
2. **阶段专用**: 每个阶段有专门的渲染逻辑
3. **实时更新**: 支持流式数据的实时处理
4. **类型安全**: 完整的 TypeScript 类型定义
5. **可扩展**: 易于添加新的阶段类型
6. **用户友好**: 直观的标签页界面和状态显示

## 部署说明

1. 新组件已集成到 `answer.tsx` 中
2. 自动检测数据格式并选择合适的组件
3. 向后兼容：旧数据仍使用旧组件
4. 可通过演示页面测试功能

这个实现完全基于你的 `planMockData` 结构设计，提供了专业的研究思维链展示界面。
