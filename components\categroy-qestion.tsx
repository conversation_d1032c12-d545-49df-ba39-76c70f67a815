import React, { useState } from 'react'
import { AiOutlineRight, AiOutlineDown } from 'react-icons/ai'
import { cn } from '@/lib/utils'
import { useConversionStore } from '@/store/useConversion'

// 图标与颜色池
const iconColors = ['#DD4265', '#54A4DB', '#F7D367', '#0000F5', '#F1D478', '#794AD0', '#F5A623']
const icons = ['🔥', '⭐', '🚀', '📘', '🎧', '📄', '🧠']

// 数据结构
const data = {
  categories: [
    {
      type: 'process',
      category: '深度问答',
      subCategories: [
        {
          subCategory: '客户反馈',
          questions: [
            '系统A在2024年Q2的客户负面反馈是否显著上升？主要集中在哪些问题？是否与生产质量波动有关？',
            '系统A长续航版（EV002）上市后，老款（EV001）的销量是否被明显分流？分流程度如何？',
            '客户反馈中‘OTA升级失败’是否集中在P202402及之后的生产批次？是否与软件版本有关？',
            '系统A在三四线城市的销量是否低于预期？是否与营销活动覆盖不足有关？'
          ]
        },
        {
          subCategory: '销售预测',
          questions: [
            '系统B的销量增长是否与其营销预算增加显著相关？ ROI 如何？',
            '系统A的客户净推荐值（NPS）是否持续低于行业平均水平？主要原因是什么？',
            '若维持当前趋势，系统A在2024年Q4的销量预计为多少？关键影响因素有哪些？'
          ]
        }
      ]
    },
    {
      type: 'kb',
      category: '知识库',
      subCategories: [
        {
          subCategory: '文本引用',
          questions: ['碳酸锂价格上涨对成本影响', '2024年4月华南暴雨对物流影响']
        },
        {
          subCategory: '视频应用',
          questions: [
            '美国关税政策预计导致物价短期上涨1.8%，接近美联储2%的通胀目标，这可能对美国消费者购车意愿和汽车行业定价策略产生哪些具体影响？',
            '吉利集团提出“转型为全球最大的机器人公司”，并强调将AI智能管家融入每辆汽车。这一战略转型面临哪些技术、市场和生态构建上的挑战？与其他车企的智能化路径相比有何独特之处？',
            '国家能源局计划制定提升充电运营服务质量的政策文件，在当前 电动汽车 市场竞争激烈、服务费已由市场自主定价的背景下，未来政策应重点从哪些方面提升用户体验与行业标准？'
          ]
        },
        {
          subCategory: '音频引用',
          questions: ['人类的五种基本感官']
        },
        {
          subCategory: '图片引用',
          questions: ['动态推测解码 Transformers库提供了哪些方法?']
        },
        {
          subCategory: 'PDF引用',
          questions: ['java编程规范有哪些']
        }
      ]
    },
    {
      type: 'web',
      category: '联网搜索',
      subCategories: [
        {
          subCategory: '实时资讯',
          questions: ['给我整理一下最新的AI资讯', '给我最新的汽车行业资讯']
        }
      ]
    },
    {
      type: 'default',
      category: '智能问数（多表查询）',
      subCategories: [
        {
          subCategory: '生产与质量分析',
          questions: [
            '生产批次缺陷率与客户反馈负面情绪的相关性分析（EV001）',
            '生产缺陷率高于1.5%的批次，其对应产品的销量是否下降？（以EV001为例）'
          ]
        },
        {
          subCategory: '销售与市场表现',
          questions: [
            '系统A在2024年各月的销量与目标对比（按区域汇总）',
            '续航低于400km的产品中，哪些有营销活动但销量仍不佳？'
          ]
        }
      ]
    }
  ]
}

interface CategoryPanelProps {
  onClick: (text: string) => void
  onDoubleClick: (text: string) => void
}

const CategoryPanel: React.FC<CategoryPanelProps> = ({ onClick, onDoubleClick }) => {
  const [expanded, setExpanded] = useState<string | null>(null)
  const { chatType, setChatType } = useConversionStore((state) => ({
    setChatType: state.setChatType,
    chatType: state.chatType
  }))

  const toggleCategory = (cat: any) => {
    setExpanded(prev => (prev === cat.category ? null : cat.category))
    if (cat.type !== chatType) {
      setChatType(cat.type);
    }
  }

  return (
    <div className="w-full p-4 bg-white rounded-md shadow-md">
      {data.categories.map((cat, idx) => {
        const icon = icons[idx % icons.length]
        const color = iconColors[idx % iconColors.length]
        const isOpen = expanded === cat.category

        return (
          <div key={cat.category} className="mb-4 border-b pb-2">
            <div
              className="flex items-center cursor-pointer text-base font-semibold hover:text-blue-600"
              onClick={() => toggleCategory(cat)}
            >
              <span className="text-xl mr-2" style={{ color }}>{icon}</span>
              <span className="flex-1">{cat.category}</span>
              {isOpen ? <AiOutlineDown /> : <AiOutlineRight />}
            </div>

            {isOpen && (
              <div className="mt-2 pl-6 space-y-3 text-sm text-gray-800">
                {cat.subCategories.map(sub => (
                  <div key={sub.subCategory}>
                    <div className="font-medium text-gray-900 mb-1">{sub.subCategory}</div>
                    <ul className="list-disc list-inside space-y-1">
                      {sub.questions.map(q => (
                        <li
                          key={q}
                          onClick={() => onClick(q)}
                          onDoubleClick={() => onDoubleClick(q)}
                          className="hover:text-blue-500 cursor-pointer"
                        >
                          {q}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

export default CategoryPanel
