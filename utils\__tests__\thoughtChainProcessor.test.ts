/**
 * 思维链数据处理器测试
 */

import { processResearchResult, convertToThoughtSteps, getCurrentActiveThought } from '../thoughtChainProcessor';

describe('ThoughtChainProcessor', () => {
  describe('processResearchResult', () => {
    it('should process planning stage correctly', () => {
      const researchResult = {
        stage: 'planning' as const,
        thought_number: 1,
        next_step_needed: true,
        research_plan: {
          overall_goal: "确定系统A在2024年Q2客户负面反馈的上升趋势"
        }
      };

      const result = processResearchResult(null, researchResult);

      expect(result.currentStage).toBe('planning');
      expect(result.currentThoughtNumber).toBe(1);
      expect(result.stages[1]['planning']).toBeDefined();
      expect(result.processedThoughts).toHaveLength(1);
      expect(result.processedThoughts[0].title).toBe('制定计划');
    });

    it('should handle multiple stages for same thought number', () => {
      let result = processResearchResult(null, {
        stage: 'planning' as const,
        thought_number: 1,
        next_step_needed: true,
        research_plan: {
          overall_goal: "研究目标"
        }
      });

      result = processResearchResult(result, {
        stage: 'rewriting' as const,
        thought_number: 1,
        next_step_needed: true,
        required_tools: [
          {
            tool_name: 'nl2sql',
            parameters: { query: '统计查询' }
          }
        ]
      });

      expect(result.stages[1]['planning']).toBeDefined();
      expect(result.stages[1]['rewriting']).toBeDefined();
      expect(result.processedThoughts).toHaveLength(1);
      expect(result.processedThoughts[0].currentStage).toBe('rewriting');
    });

    it('should handle multiple thought numbers', () => {
      let result = processResearchResult(null, {
        stage: 'planning' as const,
        thought_number: 1,
        next_step_needed: true,
        research_plan: { overall_goal: "目标1" }
      });

      result = processResearchResult(result, {
        stage: 'planning' as const,
        thought_number: 2,
        next_step_needed: false,
        research_plan: { overall_goal: "目标2" }
      });

      expect(result.processedThoughts).toHaveLength(2);
      expect(result.currentThoughtNumber).toBe(2);
      expect(result.processedThoughts[1].next_step_needed).toBe(false);
    });
  });

  describe('convertToThoughtSteps', () => {
    it('should convert processed thoughts to component format', () => {
      const processedThoughts = [
        {
          thought_number: 1,
          stages: {
            planning: {
              stage: 'planning' as const,
              thought_number: 1,
              next_step_needed: true,
              research_plan: { overall_goal: "目标" }
            }
          },
          currentStage: 'planning',
          title: '制定计划',
          content: '正在制定计划...',
          next_step_needed: true,
          status: 'thinking' as const,
          stageData: {
            planning: { overall_goal: "目标" }
          }
        }
      ];

      const steps = convertToThoughtSteps(processedThoughts);

      expect(steps).toHaveLength(1);
      expect(steps[0].id).toBe('thought-1');
      expect(steps[0].title).toBe('制定计划');
      expect(steps[0].content).toBe('正在制定计划...');
      expect(steps[0].status).toBe('thinking');
    });
  });

  describe('getCurrentActiveThought', () => {
    it('should return current active thought', () => {
      const data = {
        stages: {
          1: { planning: { stage: 'planning' as const, thought_number: 1, next_step_needed: true } },
          2: { rewriting: { stage: 'rewriting' as const, thought_number: 2, next_step_needed: false } }
        },
        currentStage: 'rewriting',
        currentThoughtNumber: 2,
        processedThoughts: [
          {
            thought_number: 1,
            stages: {},
            currentStage: 'planning',
            title: '制定计划',
            content: '',
            next_step_needed: true,
            status: 'completed' as const,
            stageData: {}
          },
          {
            thought_number: 2,
            stages: {},
            currentStage: 'rewriting',
            title: '查询重写',
            content: '',
            next_step_needed: false,
            status: 'thinking' as const,
            stageData: {}
          }
        ]
      };

      const activeThought = getCurrentActiveThought(data);

      expect(activeThought).toBeDefined();
      expect(activeThought?.thought_number).toBe(2);
      expect(activeThought?.title).toBe('查询重写');
    });

    it('should return null for empty data', () => {
      const data = {
        stages: {},
        currentStage: '',
        currentThoughtNumber: 0,
        processedThoughts: []
      };

      const activeThought = getCurrentActiveThought(data);

      expect(activeThought).toBeNull();
    });
  });

  describe('stage content generation', () => {
    it('should generate correct content for planning stage', () => {
      const researchResult = {
        stage: 'planning' as const,
        thought_number: 1,
        next_step_needed: true,
        research_plan: {
          overall_goal: "确定系统A在2024年Q2客户负面反馈的上升趋势"
        }
      };

      const result = processResearchResult(null, researchResult);
      const thought = result.processedThoughts[0];

      expect(thought.content).toContain('确定系统A在2024年Q2客户负面反馈的上升趋势');
    });

    it('should generate correct content for rewriting stage', () => {
      const researchResult = {
        stage: 'rewriting' as const,
        thought_number: 1,
        next_step_needed: true,
        required_tools: [
          {
            tool_name: 'nl2sql',
            parameters: { query: '查询1' }
          },
          {
            tool_name: 'search_web',
            parameters: { query: '查询2' }
          }
        ]
      };

      const result = processResearchResult(null, researchResult);
      const thought = result.processedThoughts[0];

      expect(thought.content).toContain('需要执行 2 个工具查询');
      expect(thought.content).toContain('1. nl2sql: 查询1');
      expect(thought.content).toContain('2. search_web: 查询2');
    });
  });
});
