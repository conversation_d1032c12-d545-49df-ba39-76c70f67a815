/**
 * 思维链数据处理器测试
 */

import { processResearchResult, convertToThoughtSteps, getCurrentActiveThought } from '../thoughtChainProcessor';

describe('ThoughtChainProcessor', () => {
  describe('processResearchResult', () => {
    it('should process first research result correctly', () => {
      const researchResult = {
        stage: 'analysis',
        thought_number: 1,
        next_step_needed: true,
        deep_analysis_result: {
          analysis_findings: {
            key_insights: [
              { insight: 'OTA升级失败问题主要集中在特定批次' }
            ]
          }
        }
      };

      const result = processResearchResult(null, researchResult);

      expect(result.currentStage).toBe('analysis');
      expect(result.currentThoughtNumber).toBe(1);
      expect(result.stages[1]['analysis']).toBeDefined();
      expect(result.processedThoughts).toHaveLength(1);
      expect(result.processedThoughts[0].thought_purpose).toBe('深度分析');
    });

    it('should handle multiple stages for same thought number', () => {
      let result = processResearchResult(null, {
        stage: 'analysis',
        thought_number: 1,
        next_step_needed: true,
        deep_analysis_result: {
          analysis_findings: {
            key_insights: [{ insight: '分析结果1' }]
          }
        }
      });

      result = processResearchResult(result, {
        stage: 'rewriting',
        thought_number: 1,
        next_step_needed: true,
        required_tools: [
          {
            tool_name: 'nl2sql',
            parameters: { query: '统计OTA升级失败数量' }
          }
        ]
      });

      expect(result.stages[1]['analysis']).toBeDefined();
      expect(result.stages[1]['rewriting']).toBeDefined();
      expect(result.processedThoughts).toHaveLength(1);
      expect(result.processedThoughts[0].currentStage).toBe('rewriting');
    });

    it('should handle multiple thought numbers', () => {
      let result = processResearchResult(null, {
        stage: 'analysis',
        thought_number: 1,
        next_step_needed: true
      });

      result = processResearchResult(result, {
        stage: 'analysis',
        thought_number: 2,
        next_step_needed: false
      });

      expect(result.processedThoughts).toHaveLength(2);
      expect(result.currentThoughtNumber).toBe(2);
      expect(result.processedThoughts[1].next_step_needed).toBe(false);
    });
  });

  describe('convertToThoughtSteps', () => {
    it('should convert processed thoughts to component format', () => {
      const processedThoughts = [
        {
          thought_number: 1,
          stages: {
            analysis: {
              stage: 'analysis',
              thought_number: 1,
              next_step_needed: true
            }
          },
          currentStage: 'analysis',
          thought_purpose: '深度分析',
          thought: '正在分析...',
          next_step_needed: true,
          status: 'thinking' as const
        }
      ];

      const steps = convertToThoughtSteps(processedThoughts);

      expect(steps).toHaveLength(1);
      expect(steps[0].id).toBe('thought-1');
      expect(steps[0].title).toBe('深度分析');
      expect(steps[0].content).toBe('正在分析...');
      expect(steps[0].status).toBe('thinking');
    });
  });

  describe('getCurrentActiveThought', () => {
    it('should return current active thought', () => {
      const data = {
        stages: {
          1: { analysis: { stage: 'analysis', thought_number: 1, next_step_needed: true } },
          2: { rewriting: { stage: 'rewriting', thought_number: 2, next_step_needed: false } }
        },
        currentStage: 'rewriting',
        currentThoughtNumber: 2,
        processedThoughts: [
          {
            thought_number: 1,
            stages: {},
            currentStage: 'analysis',
            thought_purpose: '深度分析',
            thought: '',
            next_step_needed: true,
            status: 'completed' as const
          },
          {
            thought_number: 2,
            stages: {},
            currentStage: 'rewriting',
            thought_purpose: '查询重写',
            thought: '',
            next_step_needed: false,
            status: 'thinking' as const
          }
        ]
      };

      const activeThought = getCurrentActiveThought(data);

      expect(activeThought).toBeDefined();
      expect(activeThought?.thought_number).toBe(2);
      expect(activeThought?.thought_purpose).toBe('查询重写');
    });

    it('should return null for empty data', () => {
      const data = {
        stages: {},
        currentStage: '',
        currentThoughtNumber: 0,
        processedThoughts: []
      };

      const activeThought = getCurrentActiveThought(data);

      expect(activeThought).toBeNull();
    });
  });

  describe('stage content generation', () => {
    it('should generate correct content for analysis stage', () => {
      const researchResult = {
        stage: 'analysis',
        thought_number: 1,
        next_step_needed: true,
        deep_analysis_result: {
          analysis_findings: {
            key_insights: [
              { insight: '洞察1' },
              { insight: '洞察2' }
            ]
          }
        }
      };

      const result = processResearchResult(null, researchResult);
      const thought = result.processedThoughts[0];

      expect(thought.thought).toBe('洞察1\n洞察2');
    });

    it('should generate correct content for rewriting stage', () => {
      const researchResult = {
        stage: 'rewriting',
        thought_number: 1,
        next_step_needed: true,
        required_tools: [
          {
            tool_name: 'nl2sql',
            parameters: { query: '查询1' }
          },
          {
            tool_name: 'search_web',
            parameters: { query: '查询2' }
          }
        ]
      };

      const result = processResearchResult(null, researchResult);
      const thought = result.processedThoughts[0];

      expect(thought.thought).toContain('需要执行 2 个工具查询');
      expect(thought.thought).toContain('1. nl2sql: 查询1');
      expect(thought.thought).toContain('2. search_web: 查询2');
    });
  });
});
