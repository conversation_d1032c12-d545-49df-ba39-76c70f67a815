'use client'

import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "../ui/dropdown-menu"
import { DropdownMenuItem } from "@radix-ui/react-dropdown-menu";
import { useState } from "react";
import { useDataSet } from "@/context/data-set";
import { cn } from "@/lib/utils";
import { useConversionStore } from "@/store/useConversion";
import { shallow } from "zustand/shallow";

const icons = [
  {
    color: '#64D048',
    icon: 'icon-a-002-burger',
  },
  {
    color: '#0000F5',
    icon: 'icon-a-019-corndog',
  },
  {
    color: '#344148',
    icon: 'icon-a-011-sandwich',
  },
  {
    color: '#E95138',
    icon: 'icon-a-011-sandwich',
  },
  {
    color: '#DD4265',
    icon: 'icon-remen',
  },
  {
    color: '#54A4DB',
    icon: 'icon-iconfont-',
  },
  {
    color: '#F7D367',
    icon: 'icon-xingxing',
  },
  {
    color: '#0000F5',
    icon: 'icon-xiaolian',
  },
  {
    color: '#F1D478',
    icon: 'icon-icon-taikong8',
  },
  {
    color: '#794AD0',
    icon: 'icon-1huojian',
  },
  {
    color: '#F5A623',
    icon: 'icon-a-003-donut',
  },
]

export const SelectorQuestion = () => {

  const { datasetList, changeDataset, currentDataset } = useDataSet()

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const { chatType, setChatType } = useConversionStore((state) => ({
    chatType: state.chatType,
    setChatType: state.setChatType
  }), shallow);

  return <>
    <div className="w-full relative flex items-center justify-between h-full">
      <div className="flex items-center text-sm select-none bg-[#E7EEFD] rounded-md border border-[#7FA3F8] px-2 py-1">

        <DropdownMenu
          open={isDropdownOpen}
          onOpenChange={(open) => setIsDropdownOpen(open)}
        >
          <DropdownMenuTrigger asChild>
            <button className="flex lg:text-sm text-xs items-center cursor-pointer select-none focus:outline-none text-[#42A3FA]">
              <span className="text-base lg:block hidden">📊</span>
              <span className="text-black lg:block hidden">数据表：</span>
              <span>{currentDataset.dataset_name}</span>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="lg:w-[160px] w-[80px] max-h-60 overflow-y-auto mt-2" align="center">
            {
              datasetList.map((item, index) => (
                <DropdownMenuItem
                  key={item.dataset_id}
                  onClick={() => changeDataset(item.dataset_id)}
                  className={`py-2 lg:px-4 px-2 focus:outline-none lg:text-sm text-xs rounded-lg cursor-pointer ${currentDataset.dataset_id === item.dataset_id ? 'bg-blue-100 text-blue-500' : ''
                    } hover:bg-blue-50 hover:text-blue-700`}
                >
                  <i className={cn('iconfont mr-2', icons[index % icons.length].icon)} style={{ color: icons[index % icons.length].color }}></i>
                  {item.dataset_name}
                </DropdownMenuItem>
              ))
            }
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="flex items-center space-x-2 ml-2">
        <button
          className={cn(
            'lg:px-3 px-1 py-1 rounded-md lg:text-sm text-xs',
            chatType === 'kb' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700',
            'hover:bg-blue-600 hover:text-white'
          )}
          onClick={() => setChatType(chatType === 'kb' ? 'default' : 'kb')} // 点击按钮切换 isKb 状态
        >
          知识库问答
        </button>
        <button
          className={cn(
            'lg:px-3 px-1 py-1 rounded-md lg:text-sm text-xs',
            chatType === 'web' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700',
            'hover:bg-blue-600 hover:text-white'
          )}
          onClick={() => setChatType(chatType === 'web' ? 'default' : 'web')} // 点击按钮切换 isKb 状态
        >
          联网搜索
        </button>
        <button
          className={cn(
            'lg:px-3 px-1 py-1 rounded-md lg:text-sm text-xs',
            chatType === 'process' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700',
            'hover:bg-blue-600 hover:text-white'
          )}
          onClick={() => setChatType(chatType === 'process' ? 'default' : 'process')} // 点击按钮切换 isKb 状态
        >
          深度问答
        </button>
      </div>
    </div>
  </>
}