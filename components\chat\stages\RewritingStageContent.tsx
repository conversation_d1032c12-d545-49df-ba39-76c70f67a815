/**
 * Rewriting 阶段内容组件
 * 参考 TaskExecutionPanel 的 RewritingContent 设计
 */

import React from 'react';

interface RewritingStageContentProps {
  stageData: any;
}

const SectionTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <h4 className="mb-2 text-sm font-semibold text-gray-900">{children}</h4>
);

const RewritingStageContent: React.FC<RewritingStageContentProps> = ({ stageData }) => {
  if (!stageData) {
    return <div className="text-sm text-gray-500">无重写数据</div>;
  }

  const tools = stageData.required_tools || [];

  if (tools.length === 0) {
    return <div className="text-sm text-gray-500">无需要执行的工具</div>;
  }

  return (
    <div className="space-y-4">
      <SectionTitle>所需工具与参数 ({tools.length})</SectionTitle>
      <div className="space-y-3 pr-1">
        {tools.map((tool: any, index: number) => (
          <div key={index} className="rounded-lg border p-3 text-sm">
            {/* 工具名称 */}
            <div className="flex items-center justify-between mb-2">
              <div className="font-medium text-blue-600">
                {tool.tool_name || "未命名工具"}
              </div>
              <div className="text-xs text-gray-500">
                #{index + 1}
              </div>
            </div>

            {/* 工具参数 */}
            {tool.parameters && (
              <div className="space-y-2">
                {/* 查询语句 */}
                {tool.parameters.query && (
                  <div>
                    <h5 className="text-xs font-medium text-gray-600 mb-1">查询语句</h5>
                    <div className="bg-gray-50 p-2 rounded text-xs text-gray-700 font-mono">
                      <pre className="whitespace-pre-wrap">{tool.parameters.query}</pre>
                    </div>
                  </div>
                )}

                {/* 其他参数 */}
                {Object.keys(tool.parameters).filter(key => key !== 'query').length > 0 && (
                  <div>
                    <h5 className="text-xs font-medium text-gray-600 mb-1">其他参数</h5>
                    <div className="bg-gray-50 p-2 rounded text-xs">
                      {Object.entries(tool.parameters)
                        .filter(([key]) => key !== 'query')
                        .map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="font-medium text-gray-600">{key}:</span>
                            <span className="text-gray-700 ml-2">
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 工具描述 */}
            {tool.description && (
              <div className="mt-2">
                <h5 className="text-xs font-medium text-gray-600 mb-1">描述</h5>
                <p className="text-xs text-gray-600">{tool.description}</p>
              </div>
            )}

            {/* 预期输出 */}
            {tool.expected_output && (
              <div className="mt-2">
                <h5 className="text-xs font-medium text-gray-600 mb-1">预期输出</h5>
                <p className="text-xs text-gray-600">{tool.expected_output}</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 工具统计信息 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-blue-800">工具总数:</span>
            <span className="ml-2 text-blue-700">{tools.length}</span>
          </div>
          <div>
            <span className="font-medium text-blue-800">工具类型:</span>
            <span className="ml-2 text-blue-700">
              {[...new Set(tools.map((t: any) => t.tool_name))].join(', ')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RewritingStageContent;
