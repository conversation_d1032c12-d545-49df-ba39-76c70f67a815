/**
 * 思维链数据处理工具
 * 用于处理流式输出中的 research_result 数据结构
 */

export interface ResearchStage {
  stage: string;
  thought_number: number;
  next_step_needed: boolean;
  [key: string]: any;
}

export interface ProcessedThought {
  thought_number: number;
  stages: Record<string, ResearchStage>;
  currentStage: string;
  thought_purpose: string;
  thought: string;
  next_step_needed: boolean;
  required_tools?: any[];
  status: 'pending' | 'thinking' | 'completed';
}

export interface ThoughtChainData {
  stages: Record<number, Record<string, ResearchStage>>;
  currentStage: string;
  currentThoughtNumber: number;
  processedThoughts: ProcessedThought[];
}

/**
 * 处理单个研究结果数据
 */
export function processResearchResult(
  existingData: ThoughtChainData | null,
  newResearchResult: ResearchStage
): ThoughtChainData {
  const { stage, thought_number, next_step_needed } = newResearchResult;
  
  // 初始化数据结构
  const data: ThoughtChainData = existingData || {
    stages: {},
    currentStage: stage,
    currentThoughtNumber: thought_number,
    processedThoughts: []
  };

  // 更新当前状态
  data.currentStage = stage;
  data.currentThoughtNumber = thought_number;

  // 存储阶段数据
  if (!data.stages[thought_number]) {
    data.stages[thought_number] = {};
  }
  data.stages[thought_number][stage] = {
    ...newResearchResult,
    timestamp: Date.now()
  };

  // 更新处理后的思考数据
  data.processedThoughts = generateProcessedThoughts(data.stages);

  return data;
}

/**
 * 从阶段数据生成处理后的思考链
 */
function generateProcessedThoughts(stages: Record<number, Record<string, ResearchStage>>): ProcessedThought[] {
  const thoughts: ProcessedThought[] = [];

  // 按 thought_number 排序
  const sortedThoughtNumbers = Object.keys(stages)
    .map(Number)
    .sort((a, b) => a - b);

  for (const thoughtNumber of sortedThoughtNumbers) {
    const stageData = stages[thoughtNumber];
    const stageNames = Object.keys(stageData);
    
    // 确定当前阶段（取最新的阶段）
    const currentStage = stageNames.reduce((latest, current) => {
      const latestTimestamp = stageData[latest]?.timestamp || 0;
      const currentTimestamp = stageData[current]?.timestamp || 0;
      return currentTimestamp > latestTimestamp ? current : latest;
    });

    const currentStageData = stageData[currentStage];
    
    const thought: ProcessedThought = {
      thought_number: thoughtNumber,
      stages: stageData,
      currentStage,
      thought_purpose: getStageDisplayName(currentStage),
      thought: generateThoughtContent(currentStage, currentStageData),
      next_step_needed: currentStageData.next_step_needed || false,
      required_tools: currentStageData.required_tools,
      status: determineThoughtStatus(stageData, currentStageData)
    };

    thoughts.push(thought);
  }

  return thoughts;
}

/**
 * 获取阶段的显示名称
 */
function getStageDisplayName(stage: string): string {
  const stageNames: Record<string, string> = {
    'analysis': '深度分析',
    'rewriting': '查询重写',
    'execution': '执行查询',
    'synthesis': '结果综合',
    'planning': '制定计划',
    'validation': '结果验证'
  };
  
  return stageNames[stage] || stage;
}

/**
 * 生成思考内容
 */
function generateThoughtContent(stage: string, stageData: ResearchStage): string {
  switch (stage) {
    case 'analysis':
      if (stageData.deep_analysis_result?.analysis_findings?.key_insights) {
        return stageData.deep_analysis_result.analysis_findings.key_insights
          .map((insight: any) => insight.insight)
          .join('\n');
      }
      return '正在进行深度分析...';
      
    case 'rewriting':
      if (stageData.required_tools?.length) {
        return `需要执行 ${stageData.required_tools.length} 个工具查询：\n${
          stageData.required_tools
            .map((tool: any, index: number) => `${index + 1}. ${tool.tool_name}: ${tool.parameters?.query || ''}`)
            .join('\n')
        }`;
      }
      return '正在重写查询...';
      
    case 'execution':
      return '正在执行数据查询...';
      
    case 'synthesis':
      return '正在综合分析结果...';
      
    case 'planning':
      return '正在制定执行计划...';
      
    case 'validation':
      return '正在验证结果...';
      
    default:
      return `正在执行 ${stage} 阶段...`;
  }
}

/**
 * 确定思考状态
 */
function determineThoughtStatus(
  stageData: Record<string, ResearchStage>,
  currentStageData: ResearchStage
): 'pending' | 'thinking' | 'completed' {
  // 如果有多个阶段且当前阶段不需要下一步，则认为已完成
  const stageCount = Object.keys(stageData).length;
  
  if (!currentStageData.next_step_needed) {
    return 'completed';
  }
  
  if (stageCount > 1) {
    return 'thinking';
  }
  
  return 'pending';
}

/**
 * 将处理后的思考数据转换为组件所需的格式
 */
export function convertToThoughtSteps(processedThoughts: ProcessedThought[]): any[] {
  return processedThoughts.map((thought, index) => ({
    id: `thought-${thought.thought_number}`,
    title: thought.thought_purpose,
    content: thought.thought,
    status: thought.status,
    duration: '',
    learnings: '',
    details: {
      analysis: [],
      data: [],
      conclusion: '',
      tools: thought.required_tools?.map((tool: any) => tool.tool_name) || [],
      needQuerys: thought.required_tools?.map((tool: any) => tool.parameters?.query) || [],
      results: [],
      references: {
        databases: [],
        webPages: [],
        knowledgeBase: []
      }
    },
    // 保留原始数据以供调试
    rawData: thought
  }));
}

/**
 * 获取当前活跃的思考步骤
 */
export function getCurrentActiveThought(data: ThoughtChainData): ProcessedThought | null {
  if (!data.processedThoughts.length) return null;
  
  // 返回当前 thought_number 对应的思考
  return data.processedThoughts.find(
    thought => thought.thought_number === data.currentThoughtNumber
  ) || data.processedThoughts[data.processedThoughts.length - 1];
}
