/**
 * 研究结果数据结构演示组件
 * 展示如何处理和展示多阶段思考数据
 */

import React, { useState, useEffect } from 'react';
import { processResearchResult, getCurrentActiveThought, type ThoughtChainData } from '@/utils/thoughtChainProcessor';

interface ResearchResultDemoProps {
  // 模拟流式数据输入
  streamData?: any[];
}

const ResearchResultDemo: React.FC<ResearchResultDemoProps> = ({ streamData = [] }) => {
  const [thoughtChainData, setThoughtChainData] = useState<ThoughtChainData | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  // 模拟流式数据
  const mockStreamData = [
    {
      stage: 'analysis',
      thought_number: 1,
      next_step_needed: true,
      deep_analysis_result: {
        analysis_findings: {
          key_insights: [
            { insight: 'OTA升级失败问题主要集中在P202402及之后的生产批次' },
            { insight: '客户反馈显示问题在2024年1月开始增加' }
          ]
        },
        task_status_assessment: {
          overall_goal_achieved: '部分达成',
          next_step_recommendation: '需要进一步调查'
        }
      }
    },
    {
      stage: 'rewriting',
      thought_number: 1,
      next_step_needed: true,
      required_tools: [
        {
          tool_name: 'nl2sql',
          parameters: {
            query: '统计2024年1月至今每周客户反馈中提及"OTA升级失败"的数量及占比，按周分组并按时间排序'
          }
        },
        {
          tool_name: 'nl2sql',
          parameters: {
            query: '统计不同生产批次(P202402及之后批次与其他批次对比)中客户反馈"OTA升级失败"的数量及占比，按批次分组'
          }
        }
      ]
    },
    {
      stage: 'execution',
      thought_number: 1,
      next_step_needed: true,
      tool_executions: [
        {
          tool_name: 'nl2sql',
          query: '统计2024年1月至今每周客户反馈中提及"OTA升级失败"的数量及占比',
          result: {
            retrieval_result: [
              { week: '2024-W01', count: 5, percentage: 2.1 },
              { week: '2024-W02', count: 8, percentage: 3.2 }
            ]
          }
        }
      ]
    },
    {
      stage: 'analysis',
      thought_number: 2,
      next_step_needed: true,
      deep_analysis_result: {
        analysis_findings: {
          key_insights: [
            { insight: '数据显示OTA升级失败率在特定周期内确实有显著增长' }
          ]
        }
      }
    },
    {
      stage: 'synthesis',
      thought_number: 2,
      next_step_needed: false,
      synthesis_result: {
        final_conclusion: '基于数据分析，OTA升级失败问题确实存在且有明显的时间和批次模式'
      }
    }
  ];

  const dataToUse = streamData.length > 0 ? streamData : mockStreamData;

  // 模拟流式数据接收
  useEffect(() => {
    if (currentIndex < dataToUse.length) {
      const timer = setTimeout(() => {
        const newData = dataToUse[currentIndex];
        setThoughtChainData(prev => processResearchResult(prev, newData));
        setCurrentIndex(prev => prev + 1);
      }, 2000); // 每2秒接收一个新数据

      return () => clearTimeout(timer);
    }
  }, [currentIndex, dataToUse]);

  const activeThought = thoughtChainData ? getCurrentActiveThought(thoughtChainData) : null;

  const resetDemo = () => {
    setThoughtChainData(null);
    setCurrentIndex(0);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">研究结果数据结构演示</h2>
        <p className="text-gray-600">
          这个演示展示了如何处理流式输出中的多阶段思考数据
        </p>
        <button
          onClick={resetDemo}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          重新开始演示
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 当前状态 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold mb-3">当前状态</h3>
          {thoughtChainData ? (
            <div className="space-y-2">
              <div>
                <span className="font-medium">当前阶段:</span> {thoughtChainData.currentStage}
              </div>
              <div>
                <span className="font-medium">思考编号:</span> {thoughtChainData.currentThoughtNumber}
              </div>
              <div>
                <span className="font-medium">总思考数:</span> {thoughtChainData.processedThoughts.length}
              </div>
              <div>
                <span className="font-medium">进度:</span> {currentIndex}/{dataToUse.length}
              </div>
            </div>
          ) : (
            <p className="text-gray-500">等待数据...</p>
          )}
        </div>

        {/* 当前活跃思考 */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold mb-3">当前活跃思考</h3>
          {activeThought ? (
            <div className="space-y-2">
              <div>
                <span className="font-medium">目的:</span> {activeThought.thought_purpose}
              </div>
              <div>
                <span className="font-medium">状态:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  activeThought.status === 'completed' ? 'bg-green-100 text-green-800' :
                  activeThought.status === 'thinking' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {activeThought.status}
                </span>
              </div>
              <div>
                <span className="font-medium">内容:</span>
                <p className="mt-1 text-sm text-gray-600">{activeThought.thought}</p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">无活跃思考</p>
          )}
        </div>
      </div>

      {/* 思考历史 */}
      <div className="mt-6 bg-white rounded-lg shadow p-4">
        <h3 className="text-lg font-semibold mb-3">思考历史</h3>
        {thoughtChainData?.processedThoughts.length ? (
          <div className="space-y-4">
            {thoughtChainData.processedThoughts.map((thought, index) => (
              <div key={thought.thought_number} className="border-l-4 border-blue-500 pl-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">
                    思考 {thought.thought_number}: {thought.thought_purpose}
                  </h4>
                  <span className={`px-2 py-1 rounded text-sm ${
                    thought.status === 'completed' ? 'bg-green-100 text-green-800' :
                    thought.status === 'thinking' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {thought.status}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{thought.thought}</p>
                
                {/* 阶段详情 */}
                <div className="text-xs text-gray-500">
                  <span className="font-medium">阶段:</span> {Object.keys(thought.stages).join(' → ')}
                  {thought.required_tools && (
                    <>
                      <span className="ml-4 font-medium">工具:</span> {thought.required_tools.length}个
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">暂无思考历史</p>
        )}
      </div>

      {/* 原始数据结构 */}
      <div className="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">原始数据结构</h3>
        <pre className="text-xs overflow-auto max-h-96 bg-white p-3 rounded border">
          {JSON.stringify(thoughtChainData, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default ResearchResultDemo;
