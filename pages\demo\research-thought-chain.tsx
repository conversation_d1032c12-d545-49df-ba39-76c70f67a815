/**
 * 研究思维链演示页面
 */

import React, { useState, useEffect } from 'react';
import ResearchThoughtChain from '@/components/chat/ResearchThoughtChain';
import { processResearchResult, type ThoughtChainData } from '@/utils/thoughtChainProcessor';

// 模拟 planMockData 的数据
const mockStreamData = [
  {
    stage: 'planning' as const,
    thought_number: 1,
    next_step_needed: true,
    research_plan: {
      overall_goal: "确定系统A在2024年Q2客户负面反馈的上升趋势、主要问题及与生产质量的相关性",
      tool_specific_tasks: {
        nl2sql: {
          key_business_metrics: [
            "负面反馈占比变化",
            "主要投诉问题分类统计",
            "生产批次缺陷率"
          ]
        },
        query_knowledge_base: {
          search_topics: [
            "2024年质量异常事件报告",
            "车机系统OTA升级记录"
          ]
        }
      }
    }
  },
  {
    stage: 'rewriting' as const,
    thought_number: 1,
    next_step_needed: true,
    required_tools: [
      {
        tool_name: "nl2sql",
        parameters: {
          query: "计算2024年Q1和Q2系统A产品的负面反馈占比，按月份分组展示"
        }
      },
      {
        tool_name: "query_knowledge_base",
        parameters: {
          query: "2024年Q2系统A客户NPS差评关键词统计"
        }
      }
    ]
  },
  {
    stage: 'execution' as const,
    thought_number: 1,
    next_step_needed: true,
    tool_executions: [
      {
        tool_name: "nl2sql",
        query: "计算2024年Q1和Q2系统A产品的负面反馈占比",
        result: {
          retrieval_result: [
            { month: '2024-01', negative_ratio: 0.15 },
            { month: '2024-02', negative_ratio: 0.18 }
          ]
        }
      },
      {
        tool_name: "query_knowledge_base",
        query: "2024年Q2系统A客户NPS差评关键词统计",
        result: ["续航虚标", "车机卡顿", "售后响应慢"]
      }
    ]
  },
  {
    stage: 'analysis' as const,
    thought_number: 1,
    next_step_needed: false,
    deep_analysis_result: {
      analysis_findings: {
        key_insights: [
          {
            insight: "系统A在2024年Q2的负面反馈确实呈上升趋势，主要集中在车机系统问题",
            confidence: "高"
          },
          {
            insight: "生产批次P202402及之后的车辆问题更为突出",
            confidence: "中"
          }
        ]
      },
      recommendations: [
        {
          type: "技术改进",
          action: "加速OTA 2.1.0版本推送，修复车机系统稳定性问题",
          priority: "高"
        },
        {
          type: "供应链优化",
          action: "评估MCU芯片供应商切换的可行性",
          priority: "中"
        }
      ]
    }
  },
  // 第二个思考
  {
    stage: 'planning' as const,
    thought_number: 2,
    next_step_needed: true,
    research_plan: {
      overall_goal: "深入分析MCU芯片供应商切换的技术可行性和成本效益",
      tool_specific_tasks: {
        nl2sql: {
          key_business_metrics: [
            "MCU芯片缺陷率对比",
            "供应商切换成本分析"
          ]
        }
      }
    }
  },
  {
    stage: 'rewriting' as const,
    thought_number: 2,
    next_step_needed: true,
    required_tools: [
      {
        tool_name: "nl2sql",
        parameters: {
          query: "对比MCU-8200与CH-MCU2000芯片的缺陷率和成本数据"
        }
      }
    ]
  }
];

const ResearchThoughtChainDemo: React.FC = () => {
  const [thoughtChainData, setThoughtChainData] = useState<ThoughtChainData | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  // 模拟流式数据接收
  useEffect(() => {
    if (isPlaying && currentIndex < mockStreamData.length) {
      const timer = setTimeout(() => {
        const newData = mockStreamData[currentIndex];
        setThoughtChainData(prev => processResearchResult(prev, newData));
        setCurrentIndex(prev => prev + 1);
      }, 2000);

      return () => clearTimeout(timer);
    } else if (currentIndex >= mockStreamData.length) {
      setIsPlaying(false);
    }
  }, [currentIndex, isPlaying]);

  const startDemo = () => {
    setThoughtChainData(null);
    setCurrentIndex(0);
    setIsPlaying(true);
  };

  const stopDemo = () => {
    setIsPlaying(false);
  };

  const resetDemo = () => {
    setThoughtChainData(null);
    setCurrentIndex(0);
    setIsPlaying(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">研究思维链演示</h1>
          <p className="text-gray-600 mb-4">
            基于 planMockData 数据结构的新版思维链组件演示
          </p>
          
          {/* 控制按钮 */}
          <div className="flex gap-4 mb-4">
            <button
              onClick={startDemo}
              disabled={isPlaying}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              开始演示
            </button>
            <button
              onClick={stopDemo}
              disabled={!isPlaying}
              className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:opacity-50"
            >
              暂停演示
            </button>
            <button
              onClick={resetDemo}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              重置演示
            </button>
          </div>

          {/* 状态信息 */}
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium">进度:</span> {currentIndex}/{mockStreamData.length}
              </div>
              <div>
                <span className="font-medium">状态:</span> {isPlaying ? '播放中' : '已暂停'}
              </div>
              <div>
                <span className="font-medium">思考数:</span> {thoughtChainData?.processedThoughts.length || 0}
              </div>
            </div>
          </div>
        </div>

        {/* 思维链组件 */}
        {thoughtChainData?.processedThoughts && thoughtChainData.processedThoughts.length > 0 && (
          <div className="mb-8">
            <ResearchThoughtChain
              thoughts={thoughtChainData.processedThoughts}
              isResponding={isPlaying}
              messageId="demo-message"
            />
          </div>
        )}

        {/* 原始数据展示 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">原始数据结构</h2>
          <div className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
            <pre className="text-sm">
              {JSON.stringify(thoughtChainData, null, 2)}
            </pre>
          </div>
        </div>

        {/* 说明文档 */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">功能说明</h2>
          <div className="space-y-4 text-sm text-gray-600">
            <div>
              <h3 className="font-medium text-gray-900">支持的阶段类型:</h3>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li><strong>Planning:</strong> 制定研究计划，显示研究目标和工具任务</li>
                <li><strong>Rewriting:</strong> 查询重写，显示需要执行的工具列表</li>
                <li><strong>Execution:</strong> 执行查询，显示工具执行结果</li>
                <li><strong>Analysis:</strong> 深度分析，显示关键洞察和建议</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">特性:</h3>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>支持多思考编号 (thought_number)</li>
                <li>每个思考可包含多个阶段 (stage)</li>
                <li>标签页式阶段切换</li>
                <li>实时状态更新</li>
                <li>响应式设计</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResearchThoughtChainDemo;
