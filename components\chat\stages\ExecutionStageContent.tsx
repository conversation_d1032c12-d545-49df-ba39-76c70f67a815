import React, { useState } from 'react';
import { Mo<PERSON>, Card, List, Typography } from 'antd';
import GenerateTable from '../generate-table';

interface ExecutionStageContentProps {
  stageData: any;
}

const SectionTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <h4 className="mb-2 text-sm font-semibold text-gray-900">{children}</h4>
);

const ExecutionStageContent: React.FC<ExecutionStageContentProps> = ({ stageData }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    data: any[];
    toolName: string;
  } | null>(null);

  if (!stageData) {
    return <div className="text-sm text-gray-500">无执行数据</div>;
  }

  const executions = stageData.tool_executions || [];

  if (executions.length === 0) {
    return <div className="text-sm text-gray-500">无执行记录</div>;
  }

  const handleShowTable = (execution: any) => {
    const rows = execution?.result?.retrieval_result || [];
    if (Array.isArray(rows) && rows.length > 0) {
      setModalData({
        title: execution.query || '执行结果',
        data: rows,
        toolName: execution.tool_name || '未知工具'
      });
      setModalVisible(true);
    }
  };

  return (
    <div className="space-y-4">
      <SectionTitle>执行记录 ({executions.length})</SectionTitle>

      <div className="space-y-4">
        {executions.map((execution: any, index: number) => {
          const isNL2SQL = (execution?.tool_name || "").toLowerCase() === "nl2sql";
          const rows = execution?.result?.retrieval_result || [];
          const hasTableData = Array.isArray(rows) && rows.length > 0;
          const count = hasTableData ? rows.length : 0;

          return (
            <Card
              key={index}
              className="border shadow-md mb-4"  // ← 添加 mb-4 类
              variant="borderless"
              style={{
                margin: '10px 0'
              }}
            >
              {/* 工具信息头部 */}
              <div className="flex items-center justify-between mb-2 p-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-purple-600">
                    {execution.tool_name || "未知工具"}
                  </span>
                  <span className="text-xs text-gray-500">#{index + 1}</span>
                </div>
                {isNL2SQL && count > 0 && (
                  <div className="text-xs text-gray-500">
                    共 {count} 条结果
                  </div>
                )}
              </div>

              {/* 查询语句 - 可点击弹出表格 */}
              {execution.query && (
                <div className="mb-2 p-2">
                  <h5 className="text-xs font-medium text-gray-600 mb-1">查询语句</h5>
                  {isNL2SQL && hasTableData ? (
                    <button
                      onClick={() => handleShowTable(execution)}
                      className="w-full text-left rounded-md border bg-gray-50 p-2 text-xs text-gray-700 hover:bg-gray-100 transition-colors cursor-pointer"
                    >
                      <div className="font-mono whitespace-pre-wrap">{execution.query}</div>
                      <div className="mt-1 text-xs text-blue-600">
                        点击查看 {count} 条执行结果 →
                      </div>
                    </button>
                  ) : (
                    <div className="bg-gray-50 p-2 rounded text-xs text-gray-700 font-mono">
                      <pre className="whitespace-pre-wrap">{execution.query}</pre>
                    </div>
                  )}
                </div>
              )}

              {/* 执行状态 */}
              <div className="flex items-center gap-4 text-xs p-2">
                <div className="flex items-center gap-1">
                  <span className="font-medium text-gray-600">状态:</span>
                  <span className={`px-2 py-1 rounded ${execution.result ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                    {execution.result ? '执行成功' : '执行中'}
                  </span>
                </div>

                {execution.execution_time && (
                  <div className="flex items-center gap-1">
                    <span className="font-medium text-gray-600">耗时:</span>
                    <span className="text-gray-700">{execution.execution_time}</span>
                  </div>
                )}
              </div>

              {/* 非表格结果预览 */}
              {execution.result && !isNL2SQL && (
                <div className="mt-2 p-2">
                  <h5 className="text-xs font-medium text-gray-600 mb-1">执行结果</h5>
                  <div className="bg-gray-50 p-2 rounded text-xs text-gray-700 max-h-32 overflow-auto">
                    <pre className="whitespace-pre-wrap">
                      {typeof execution.result === 'string'
                        ? execution.result
                        : JSON.stringify(execution.result, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {/* 错误信息 */}
              {execution.error && (
                <div className="mt-2 p-2">
                  <h5 className="text-xs font-medium text-red-600 mb-1">错误信息</h5>
                  <div className="bg-red-50 border border-red-200 p-2 rounded text-xs text-red-700">
                    {execution.error}
                  </div>
                </div>
              )}
            </Card>
          );
        })}
      </div>

      {/* 表格弹窗 */}
      <Modal
        title={
          <div>
            <div className="font-medium">{modalData?.toolName}</div>
            <div className="text-sm text-gray-500 font-normal mt-1">
              {modalData?.title}
            </div>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width="90%"
        style={{ maxWidth: '1200px' }}
        styles={{
          body: { maxHeight: '70vh', overflow: 'auto' }
        }}
      >
        {modalData?.data && (
          <GenerateTable dataSource={modalData.data} />
        )}
      </Modal>
    </div>
  );
};

export default ExecutionStageContent;