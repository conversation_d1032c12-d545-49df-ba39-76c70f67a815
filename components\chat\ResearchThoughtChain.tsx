/**
 * 研究思维链组件
 * 基于 planMockData 数据结构设计的新版思维链组件
 */

import React, { useState, useEffect } from 'react';
import { LuChevronDown, LuChevronUp, LuLoader, LuCheck, LuClock, LuBrain } from 'react-icons/lu';
import { type ProcessedThought } from '@/utils/thoughtChainProcessor';

interface ResearchThoughtChainProps {
  thoughts: ProcessedThought[];
  isResponding?: boolean;
  messageId: string;
}

const ResearchThoughtChain: React.FC<ResearchThoughtChainProps> = ({ 
  thoughts, 
  isResponding = false, 
  messageId 
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeThought, setActiveThought] = useState<number | null>(null);
  const [activeStage, setActiveStage] = useState<string>('planning');

  // 自动激活最新的思考
  useEffect(() => {
    if (thoughts && thoughts.length > 0) {
      const latestThought = thoughts[thoughts.length - 1];
      setActiveThought(latestThought.thought_number);
      setActiveStage(latestThought.currentStage);
    }
  }, [thoughts]);

  if (!thoughts || thoughts.length === 0) {
    return null;
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'planning':
        return <LuBrain className="w-4 h-4" />;
      case 'rewriting':
        return <LuClock className="w-4 h-4" />;
      case 'execution':
        return <LuLoader className="w-4 h-4" />;
      case 'analysis':
        return <LuCheck className="w-4 h-4" />;
      default:
        return <LuBrain className="w-4 h-4" />;
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'planning':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'rewriting':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'execution':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'analysis':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <LuCheck className="w-4 h-4 text-green-600" />;
      case 'thinking':
        return <LuLoader className="w-4 h-4 text-blue-600 animate-spin" />;
      default:
        return <LuClock className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderStageContent = (thought: ProcessedThought, stage: string) => {
    const stageData = thought.stages[stage];
    if (!stageData) return null;

    switch (stage) {
      case 'planning':
        return (
          <div className="space-y-3">
            {stageData.research_plan?.overall_goal && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-1">研究目标</h4>
                <p className="text-sm text-gray-600">{stageData.research_plan.overall_goal}</p>
              </div>
            )}
            {stageData.research_plan?.tool_specific_tasks && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">工具任务</h4>
                <div className="space-y-2">
                  {Object.entries(stageData.research_plan.tool_specific_tasks).map(([tool, tasks]: [string, any]) => (
                    <div key={tool} className="bg-gray-50 p-2 rounded">
                      <span className="font-medium text-xs text-gray-700">{tool}</span>
                      {tasks.key_business_metrics && (
                        <ul className="mt-1 text-xs text-gray-600 list-disc list-inside">
                          {tasks.key_business_metrics.slice(0, 3).map((metric: string, idx: number) => (
                            <li key={idx}>{metric}</li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'rewriting':
        return (
          <div className="space-y-3">
            {stageData.required_tools && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">
                  需要执行的工具 ({stageData.required_tools.length})
                </h4>
                <div className="space-y-2">
                  {stageData.required_tools.map((tool: any, idx: number) => (
                    <div key={idx} className="bg-gray-50 p-2 rounded">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-xs text-blue-600">{tool.tool_name}</span>
                      </div>
                      <p className="text-xs text-gray-600">{tool.parameters?.query}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'execution':
        return (
          <div className="space-y-3">
            {stageData.tool_executions && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">
                  执行结果 ({stageData.tool_executions.length})
                </h4>
                <div className="space-y-2">
                  {stageData.tool_executions.slice(0, 3).map((execution: any, idx: number) => (
                    <div key={idx} className="bg-gray-50 p-2 rounded">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-xs text-purple-600">{execution.tool_name}</span>
                        <span className="text-xs text-gray-500">
                          {execution.result ? '✓ 成功' : '⏳ 执行中'}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 truncate">{execution.query}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'analysis':
        return (
          <div className="space-y-3">
            {stageData.deep_analysis_result?.analysis_findings?.key_insights && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">关键洞察</h4>
                <div className="space-y-2">
                  {stageData.deep_analysis_result.analysis_findings.key_insights.map((insight: any, idx: number) => (
                    <div key={idx} className="bg-green-50 p-2 rounded border-l-2 border-green-200">
                      <p className="text-sm text-gray-700">{insight.insight}</p>
                      {insight.confidence && (
                        <span className="text-xs text-green-600 mt-1 inline-block">
                          置信度: {insight.confidence}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            {stageData.deep_analysis_result?.recommendations && (
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-2">建议</h4>
                <div className="space-y-1">
                  {stageData.deep_analysis_result.recommendations.slice(0, 3).map((rec: any, idx: number) => (
                    <div key={idx} className="text-sm text-gray-600">
                      <span className="font-medium">{rec.type}:</span> {rec.action}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="text-sm text-gray-600">
            <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(stageData, null, 2)}</pre>
          </div>
        );
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* 头部 */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <LuBrain className="w-5 h-5 text-blue-600" />
          <div>
            <h3 className="font-medium text-gray-900">研究思维链</h3>
            <p className="text-sm text-gray-500">
              {thoughts.length} 个思考阶段 {isResponding && '• 正在思考中...'}
            </p>
          </div>
        </div>
        {isExpanded ? <LuChevronUp className="w-5 h-5 text-gray-400" /> : <LuChevronDown className="w-5 h-5 text-gray-400" />}
      </div>

      {/* 内容 */}
      {isExpanded && (
        <div className="border-t border-gray-200">
          {/* 思考列表 */}
          <div className="p-4 space-y-4">
            {thoughts.map((thought) => (
              <div key={thought.thought_number} className="border border-gray-200 rounded-lg">
                {/* 思考头部 */}
                <div 
                  className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
                  onClick={() => setActiveThought(
                    activeThought === thought.thought_number ? null : thought.thought_number
                  )}
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(thought.status)}
                    <div>
                      <h4 className="font-medium text-gray-900">
                        思考 {thought.thought_number}: {thought.title}
                      </h4>
                      <p className="text-sm text-gray-500">{thought.content}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {/* 阶段标签 */}
                    <div className="flex gap-1">
                      {Object.keys(thought.stages).map((stage) => (
                        <span 
                          key={stage}
                          className={`px-2 py-1 text-xs rounded border ${getStageColor(stage)} ${
                            stage === thought.currentStage ? 'ring-1 ring-offset-1 ring-blue-300' : ''
                          }`}
                        >
                          {getStageIcon(stage)}
                        </span>
                      ))}
                    </div>
                    {activeThought === thought.thought_number ? 
                      <LuChevronUp className="w-4 h-4 text-gray-400" /> : 
                      <LuChevronDown className="w-4 h-4 text-gray-400" />
                    }
                  </div>
                </div>

                {/* 思考详情 */}
                {activeThought === thought.thought_number && (
                  <div className="border-t border-gray-200">
                    {/* 阶段标签页 */}
                    <div className="flex border-b border-gray-200">
                      {Object.keys(thought.stages).map((stage) => (
                        <button
                          key={stage}
                          className={`px-4 py-2 text-sm font-medium border-b-2 ${
                            activeStage === stage
                              ? 'border-blue-500 text-blue-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700'
                          }`}
                          onClick={() => setActiveStage(stage)}
                        >
                          <div className="flex items-center gap-2">
                            {getStageIcon(stage)}
                            {stage}
                          </div>
                        </button>
                      ))}
                    </div>

                    {/* 阶段内容 */}
                    <div className="p-4">
                      {renderStageContent(thought, activeStage)}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ResearchThoughtChain;
