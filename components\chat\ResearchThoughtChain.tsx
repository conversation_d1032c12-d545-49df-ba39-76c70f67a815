/**
 * 研究思维链组件 - 左右分栏布局方案
 * 左侧：思考列表
 * 右侧：思考详情 + 阶段内容
 */

import React, { useState, useEffect } from "react";
import {
  LuChevronRight,
  <PERSON>Loader,
  LuCheck,
  LuClock,
  LuBrain,
} from "react-icons/lu";
import { type ProcessedThought } from "@/utils/thoughtChainProcessor";
import PlanningStageContent from "./stages/PlanningStageContent";
import RewritingStageContent from "./stages/RewritingStageContent";
import ExecutionStageContent from "./stages/ExecutionStageContent";
import AnalysisStageContent from "./stages/AnalysisStageContent";

interface ResearchThoughtChainProps {
  thoughts: ProcessedThought[];
  isResponding?: boolean;
  messageId: string;
}

const ResearchThoughtChain: React.FC<ResearchThoughtChainProps> = ({
  thoughts,
  isResponding = false,
}) => {
  const [activeThought, setActiveThought] = useState<number | null>(null);
  const [activeStage, setActiveStage] = useState<string>("planning");

  // 自动激活最新的思考
  useEffect(() => {
    if (thoughts && thoughts.length > 0) {
      const latestThought = thoughts[thoughts.length - 1];
      setActiveThought(latestThought.thought_number);
      setActiveStage(latestThought.currentStage);
    }
  }, [thoughts]);

  if (!thoughts || thoughts.length === 0) {
    return null;
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case "planning":
        return <LuBrain className="w-4 h-4" />;
      case "rewriting":
        return <LuClock className="w-4 h-4" />;
      case "execution":
        return <LuLoader className="w-4 h-4" />;
      case "analysis":
        return <LuCheck className="w-4 h-4" />;
      default:
        return <LuBrain className="w-4 h-4" />;
    }
  };

  const renderStageContent = (thought: ProcessedThought, stage: string) => {
    const stageData = thought.stages[stage];
    if (!stageData) return null;

    switch (stage) {
      case "planning":
        return <PlanningStageContent stageData={stageData} />;
      case "rewriting":
        return <RewritingStageContent stageData={stageData} />;
      case "execution":
        return <ExecutionStageContent stageData={stageData} />;
      case "analysis":
        return <AnalysisStageContent stageData={stageData} />;
      default:
        return (
          <div className="text-sm text-gray-600">
            <pre className="whitespace-pre-wrap text-xs">
              {JSON.stringify(stageData, null, 2)}
            </pre>
          </div>
        );
    }
  };

  const selectedThought = thoughts.find(
    (t) => t.thought_number === activeThought
  );

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm h-[600px] flex">
      {/* 左侧列表 */}
      <div className="w-40 border-r border-gray-200 overflow-y-auto">
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-medium text-gray-900 flex items-center gap-2">
            <LuBrain className="w-5 h-5 text-blue-600" />
            研究思维链
          </h3>
          <p className="text-sm text-gray-500">
            {thoughts.length} 个思考阶段{" "}
            {isResponding && "• 正在思考中..."}
          </p>
        </div>
        <div className="divide-y divide-gray-200">
          {thoughts.map((thought) => (
            <div
              key={thought.thought_number}
              onClick={() => {
                setActiveThought(thought.thought_number);
                setActiveStage(thought.currentStage);
              }}
              className={`p-4 cursor-pointer flex items-center justify-between rounded-lg transition-all duration-200 ${activeThought === thought.thought_number
                  ? 'bg-gradient-to-r from-blue-50 to-indigo-50 shadow-sm'   // 激活：柔和渐变 + 轻微阴影
                  : 'hover:bg-gray-50'                                       // 默认：悬停浅灰
                }`}
            >
              <div>
                <h4 className="font-medium text-gray-900 text-sm">
                  思考序号 #{thought.thought_number}
                </h4>
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                  {thought.content}
                </p>
              </div>
              <LuChevronRight className="w-4 h-4 text-gray-400" />
            </div>
          ))}
        </div>
      </div>

      {/* 右侧详情 */}
      <div className="flex-1 flex flex-col">
        {selectedThought ? (
          <>
            {/* 阶段 tab */}
            <div className="flex border-b border-gray-200 bg-white">
              {Object.keys(selectedThought.stages).map((stage) => (
                <button
                  key={stage}
                  className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${activeStage === stage
                    ? "border-blue-500 text-blue-600 bg-blue-50"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                    }`}
                  onClick={() => setActiveStage(stage)}
                >
                  <div className="flex items-center gap-2 capitalize">
                    {getStageIcon(stage)}
                    {stage}
                  </div>
                </button>
              ))}
            </div>

            {/* 阶段内容 */}
            <div className="p-4 overflow-y-auto flex-1 bg-white">
              {renderStageContent(selectedThought, activeStage)}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-400 text-sm">
            请选择左侧的思考序号查看详情
          </div>
        )}
      </div>
    </div>
  );
};

export default ResearchThoughtChain;
