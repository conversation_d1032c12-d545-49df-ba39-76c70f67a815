import React, { memo, useEffect } from 'react';
import Markdown from '../custom/markdown';
import Operate from './operate/operate';
import Retrieval from './retrieval';
import MessageLoading from '../custom/message-loading/message-loading';
import { useConversionStore } from '@/store/useConversion';
import { hasValue } from '@/lib/utils';
import useIsMobile from '@/hooks/useIsMobile';
import { RagReference } from './rag-reference';
import { WebSearchUrls } from './web-search-urls';
import ResearchThoughtChain from './ResearchThoughtChain';
import { useSidePanelStore } from '@/store/useSidePanel';

type RawChunk = Record<string, any>;

/**
 * 标准化单个片段
 */
function normalizeChunk(raw: any): any {
  // 文件格式推断
  let file_format = raw.file_format;
  if (!file_format && raw.file_name) {
    const ext = raw.file_name.split('.').pop()?.toLowerCase();
    file_format = ext;
  }

  // type 识别
  let type = '';
  if (file_format === 'pdf') {
    type = 'pdf';
  } else if (['mp4', 'mov', 'avi', 'wmv', 'webm'].includes(file_format)) {
    type = 'video';
  } else if (['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg'].includes(file_format)) {
    type = 'audio';
  } else if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg'].includes(file_format)) {
    type = 'image';
  }

  return {
    file_name: raw.file_name || '',
    file_path: raw.file_path || '',
    file_format: file_format || '',
    type,
    source: `${raw.file_path}` || '',
    file_size: raw.file_size,
    word_count: raw.word_count,
    chunk_order: typeof raw.chunk_order === 'number' ? raw.chunk_order : 0,
    total_pages: raw.total_pages,
    page: raw.page,
    page_label: raw.page_label,
    content_text: raw.content_text || raw.fragment || '',
    created_time: raw.created_time || raw.creationdate,
    modified_time: raw.modified_time || raw.moddate,
    end_ms: raw.end_ms || 0,
    start_ms: raw.start_ms || 0,
  };
}

/**
 * 批量标准化
 */
function normalizeChunkList(rawList: RawChunk[]): any[] {
  return rawList.map(normalizeChunk);
}

interface AnswerProps {
  item: any
  isLast: boolean
}

const Answer = ({ item, isLast }: AnswerProps) => {
  const isMobile = useIsMobile();
  const { isResponding, curConversionId, isCollapsed, changeCollapsed } = useConversionStore((state) => ({
    isResponding: state.isResponding,
    curConversionId: state.curConversionId,
    isCollapsed: state.isCollapsed,
    changeCollapsed: state.changeCollapsed
  }))
  const { openSidePanel } = useSidePanelStore();

  const hasUrls = Array.isArray(item.metadata?.web_metadata) && item.metadata?.web_metadata.length > 0;
  const hasRagRef = Array.isArray(item.metadata?.chunk_metadata) && item.metadata?.chunk_metadata.length > 0;
  const hasThoughts = Array.isArray(item.thoughts) && item.thoughts.length > 0;
  const hasResearchResult = item.research_result && item.research_result.processedThoughts && item.research_result.processedThoughts.length > 0;
  const finalReport = item.thoughts_info?.final_report_html || '';
  const hasReport = item.get_final_report || finalReport;


  // 修改判断条件，结合messageLoading状态
  const isLoading = isResponding && !item.content && !hasValue(item.retrieval_sql) && !hasValue(item.retrieval_result) && !hasValue(item.chart_data) && !hasThoughts
  const isShowRetrieval =
    hasValue(item.retrieval_sql) ||
    hasValue(item.retrieval_result) ||
    hasValue(item.chart_data)

  useEffect(() => {
    // 只有在流式输出且是最后一条消息时才自动打开侧边栏
    if (hasReport && isLast && item.conversation_id === curConversionId) {
      openSidePanel('report');
      if (!isCollapsed) {
        changeCollapsed()
      }
    }
  }, [hasReport, isLast, item.conversation_id, curConversionId]);

  return (
    <div className='flex space-y-2 items-start group w-full'>
      <div className='lg:w-10 w-0 h-10 rounded-full overflow-hidden flex-shrink-0'>
        <img
          src="/robat.png"
          alt=""
          className='w-full h-full object-cover' />
      </div>
      {
        isLoading ? (
          <MessageLoading className='ml-2' />
        ) : (
          <div className='flex flex-col ml-2 lg:max-w-[88%] max-w-[99%] flex-grow'>
            {
              isShowRetrieval && <Retrieval
                table={item.retrieval_result}
                sql={item.retrieval_sql}
                echart={item.chart_data}
              />
            }

            {/* 新版研究思维链组件 */}
            {hasResearchResult && (
              <ResearchThoughtChain
                thoughts={item.research_result.processedThoughts}
                isResponding={item.isThinking && isLast && item.conversation_id === curConversionId}
                messageId={item.id}
              />
            )}

            {/* 思考中状态显示 */}
            {item.isThinking && isLast && !hasThoughts && !hasResearchResult && (
              <div className="flex items-center space-x-2 p-4 bg-blue-50 rounded-lg border border-blue-200 mb-4">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-blue-700 text-sm">正在深度思考中...</span>
              </div>
            )}

            <div>
              {item.queried_table_comment && <div className='mb-2'>目标数据表：{item.queried_table_comment}</div>}
              <Markdown content={item.content} metadata={item.metadata} />
            </div>
            {hasRagRef && (
              <RagReference chunkMetadata={normalizeChunkList(item.metadata.chunk_metadata)} />
            )}

            {hasUrls && (
              <WebSearchUrls urls={item.metadata.web_metadata} />
            )}

            {
              !isResponding && item.content && (
                <div className={`${isMobile ? 'opacity-100' :
                  isLast ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                  } transition-opacity duration-200`}>
                  <Operate item={item} isLast={isLast} />
                </div>
              )
            }

          </div>
        )
      }
    </div>
  );
}

export default memo(Answer);
