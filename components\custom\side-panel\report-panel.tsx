'use client'

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ReportData, useReportStore } from '@/store/useReport';
import { shallow } from 'zustand/shallow';
import { LuEye, LuCode, LuDownload, LuFileText } from 'react-icons/lu';
import { FaFilePdf, FaFileCode, FaFileImage } from "react-icons/fa6";
import { GoScreenFull } from "react-icons/go";
import { BsFullscreenExit } from "react-icons/bs";
// import SimpleMarkdown from './simple-markdown';
import SimpleHtmlMarkdown from './simple-html-markdown';
// import Markdown from '../markdown';

export const ReportPanel: React.FC = () => {
  const [parsedHtml, setParsedHtml] = useState('');

  // 从 ReportStore 中获取当前流式报告、更新中的消息ID、以及按消息ID获取最终报告的方法
  const { currentReport, updatingMessageId, getReportByMessageId, isReportLoading, reportsByMessageId } = useReportStore();

  // TODO: 这里后续由外部传入或通过接口确定当前要展示的消息ID
  const targetMessageId = currentReport?.msgId;

  // 计算实际应展示的报告：
  // - 若 targetMessageId 为空，则沿用 currentReport（兼容现状）
  // - 若 targetMessageId 等于 updatingMessageId，则显示流式报告 currentReport
  // - 否则，优先展示已缓存的最终报告
  const effectiveReport: ReportData | null = useMemo(() => {
    if (!targetMessageId) return currentReport;
    if (updatingMessageId && updatingMessageId === targetMessageId) return currentReport;
    const cached = getReportByMessageId(targetMessageId);
    // 当缓存未及时写入时，优先回退到 currentReport（若其 msgId 匹配且有内容），避免首次打开 race 问题
    if (cached) return cached;
    if (currentReport && currentReport.msgId === targetMessageId) return currentReport;
    return null;
  }, [currentReport, updatingMessageId, getReportByMessageId, targetMessageId]);

  // 引用 HTML 源码区域，用于代码模式下的滚动
  const htmlContainerRef = useRef<HTMLDivElement>(null);
  // 引用整个报告容器，用于控制悬浮按钮的显示
  const reportContainerRef = useRef<HTMLDivElement>(null);
  // 是否全屏
  const [isFullScreen, setIsFullScreen] = useState(false)
  // 导出loading状态
  const [isExporting, setIsExporting] = useState(false);
  const [exportType, setExportType] = useState<string>('');
  const [showExportMenu, setShowExportMenu] = useState(false);

  // 视图模式：报告(report)、预览(preview) 或 代码(code)
  const [viewMode, setViewMode] = useState<'code' | 'preview'>('code');
  // 控制悬浮按钮的显示
  const [showButtons, setShowButtons] = useState(false);
  // 控制 iframe 的加载状态
  const [isIframeLoading, setIsIframeLoading] = useState(false);

  const htmlContent = effectiveReport?.htmlContent || '';
  const msgId = effectiveReport?.msgId;

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!htmlContent) {
        setParsedHtml('');
        return;
      }

      const match = htmlContent.match(/```(?:html)?\s*([\s\S]*?)\s*```/);
      if (match?.[1]) {
        setParsedHtml(match[1].trim());
      } else {
        setParsedHtml(htmlContent);
      }
    }, 300); // 节流 300ms

    return () => clearTimeout(timeoutId);
  }, [htmlContent]);

  useEffect(() => {
    setViewMode('code');
  }, [msgId])

  // 注意：展示逻辑已改为从 store 中直接选择，不再手动订阅和维护本地 report 状态。

  // 当在代码模式下，且有新的内容时，自动滚动到底部
  useEffect(() => {
    if (viewMode === 'code' && htmlContainerRef.current) {
      const scrollToBottom = () => {
        if (htmlContainerRef.current) {
          htmlContainerRef.current.scrollTop = htmlContainerRef.current.scrollHeight;
        }
      };

      // 立即滚动一次
      scrollToBottom();

      // 使用 setTimeout 确保在组件渲染完成后再次滚动
      const timer1 = setTimeout(scrollToBottom, 0);
      const timer2 = setTimeout(scrollToBottom, 50);
      const timer3 = setTimeout(scrollToBottom, 100);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [htmlContent, viewMode]);

  // 当报告内容存在且不在加载中时，自动切换到预览模式
  useEffect(() => {
    if (htmlContent && !isReportLoading) {
      setViewMode('preview');
    }
  }, [htmlContent, isReportLoading]);


  // 当 viewMode 切换到 'preview' 或 htmlContent 发生变化时，显示 loading
  useEffect(() => {
    if (viewMode === 'preview') {
      // 每次切换到预览或内容更新时，都显示 loading
      setIsIframeLoading(true);
    }
  }, [viewMode, htmlContent]); // 依赖 viewMode 和 htmlContent

  // 当 iframe 加载完成时，隐藏 loading
  const handleIframeLoad = () => {
    setIsIframeLoading(false);
  };

  const toggleFullscreen = () => {
    const container = reportContainerRef.current;
    if (!container) return;

    if (document.fullscreenElement) {
      document.exitFullscreen(); // 状态会由事件监听自动更新
    } else {
      container.requestFullscreen().catch((err) => {
        console.error("无法进入全屏:", err);
      });
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    // 清理事件监听
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 点击外部关闭导出菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showExportMenu && !target.closest('.export-dropdown')) {
        setShowExportMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showExportMenu]);

  // 获取截图数据的通用函数
  const getScreenshotData = async () => {
    const previewIframe = document.querySelector('iframe[title="报告内容"]') as HTMLIFrameElement;

    if (previewIframe && previewIframe.contentDocument) {
      // 等待iframe内容完全加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      const iframeBody = previewIframe.contentDocument.body;
      const modernScreenshot = await import('modern-screenshot');

      return await modernScreenshot.domToDataUrl(iframeBody, {
        scale: 2,
        backgroundColor: '#ffffff',
        width: 794
      });
    }
    return null;
  };

  // PDF导出
  const handleExportPDF = async () => {
    if (!parsedHtml) {
      alert('没有可导出的内容');
      return;
    }

    setIsExporting(true);
    setExportType('PDF');

    try {
      const [jsPDF] = await Promise.all([
        import('jspdf')
      ]);

      const dataUrl = await getScreenshotData();
      if (!dataUrl) {
        alert('无法获取报告内容');
        return;
      }

      const pdf = new jsPDF.jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const img = new Image();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = dataUrl;
      });

      const imgWidth = 210;
      const imgHeight = (img.height * imgWidth) / img.width;
      const pageHeight = 297;

      let heightLeft = imgHeight;
      let position = 0;

      pdf.addImage(dataUrl, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(dataUrl, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      const filename = `分析报告_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(filename);
      console.log('PDF导出成功');

    } catch (error: any) {
      console.error('PDF导出失败:', error);
      alert(`PDF导出失败: ${error.message}`);
    } finally {
      setIsExporting(false);
      setExportType('');
    }
  };

  // HTML导出 - 保存完整的交互式报告
  const handleExportHTML = async () => {
    if (!parsedHtml) {
      alert('没有可导出的内容');
      return;
    }

    setIsExporting(true);
    setExportType('HTML');

    try {
      console.log('开始HTML导出...');

      // 直接获取iframe的完整HTML内容
      const previewIframe = document.querySelector('iframe[title="报告内容"]') as HTMLIFrameElement;

      let htmlContent = '';
      if (previewIframe && previewIframe.contentDocument) {
        // 直接获取iframe的完整HTML
        htmlContent = previewIframe.contentDocument.documentElement.outerHTML;
      } else {
        // 如果没有iframe，使用原始HTML内容
        htmlContent = parsedHtml;
      }

      // 创建Blob并下载
      const blob = new Blob([htmlContent], {
        type: 'text/html;charset=utf-8'
      });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `数据分析报告_${new Date().toISOString().split('T')[0]}.html`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('HTML导出成功');

    } catch (error: any) {
      console.error('HTML导出失败:', error);
      alert(`HTML导出失败: ${error.message}`);
    } finally {
      setIsExporting(false);
      setExportType('');
    }
  };

  // 图片导出 - 替代复杂的PPT导出
  const handleExportImage = async () => {
    if (!parsedHtml) {
      alert('没有可导出的内容');
      return;
    }

    setIsExporting(true);
    setExportType('Image');

    try {
      console.log('开始图片导出...');
      const dataUrl = await getScreenshotData();
      if (!dataUrl) {
        alert('无法获取报告内容');
        return;
      }

      // 直接下载图片
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = `分析报告_${new Date().toISOString().split('T')[0]}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('图片导出成功');

    } catch (error: any) {
      console.error('图片导出失败:', error);
      alert(`图片导出失败: ${error.message}`);
    } finally {
      setIsExporting(false);
      setExportType('');
    }
  };

  return (
    <>
      <div
        ref={reportContainerRef}
        className="w-full h-full flex flex-col relative"
        onMouseEnter={() => setShowButtons(true)}
        onMouseLeave={() => setShowButtons(false)}
      >
        {/* ========== 悬浮按钮组 ========== */}
        {htmlContent && !isReportLoading && showButtons && (
          <div className="absolute top-4 left-4 z-30 flex space-x-2">
            {/* <button
              className={`cursor-pointer px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1 ${viewMode === 'report'
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 shadow-sm'
                }`}
              onClick={() => setViewMode('report')}
            >
              <LuFileText className="w-3 h-3" />
              <span>报告</span>
            </button> */}

            <button
              className={`cursor-pointer px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1 ${viewMode === 'preview'
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 shadow-sm'
                }`}
              onClick={() => setViewMode('preview')}
            >
              <LuEye className="w-3 h-3" />
              <span>预览</span>
            </button>

            <button
              className={`cursor-pointer px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1 ${viewMode === 'code'
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 shadow-sm'
                }`}
              onClick={() => setViewMode('code')}
            >
              <LuCode className="w-3 h-3" />
              <span>代码</span>
            </button>
          </div>
        )}
        {/* ========== 操作按钮组 */}
        {
          htmlContent && !isReportLoading && showButtons && (
            <div className="absolute top-4 right-4 z-30 flex space-x-2">
              <button
                className={`cursor-pointer px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1 bg-[#007bff] text-white shadow-md`}
                onClick={toggleFullscreen}
              >
                {isFullScreen ? <BsFullscreenExit className="w-3 h-3" /> : <GoScreenFull className="w-3 h-3" />}
                <span>{isFullScreen ? '退出全屏' : '全屏'}</span>
              </button>

              {/* 导出下拉菜单 */}
              <div className="relative export-dropdown">
                <button
                  className={`cursor-pointer px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1 ${isExporting
                    ? 'bg-[#059669] text-white'
                    : 'bg-[#10B981] hover:bg-[#059669] text-white'
                    } shadow-md`}
                  onClick={() => setShowExportMenu(!showExportMenu)}
                  disabled={isExporting}
                >
                  <LuDownload className="w-3 h-3" />
                  <span>{isExporting ? `${exportType}导出中...` : '导出'}</span>
                  <svg className={`w-3 h-3 transition-transform ${showExportMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* 下拉菜单 */}
                {showExportMenu && !isExporting && (
                  <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-40">
                    <button
                      className="w-full px-3 py-2 text-xs text-left hover:bg-gray-50 flex items-center space-x-2 border-b border-gray-100"
                      onClick={() => {
                        setShowExportMenu(false);
                        handleExportPDF();
                      }}
                    >
                      <FaFilePdf className="w-3 h-3 text-red-500" />
                      <span>导出PDF</span>
                    </button>

                    <button
                      className="w-full px-3 py-2 text-xs text-left hover:bg-gray-50 flex items-center space-x-2 border-b border-gray-100"
                      onClick={() => {
                        setShowExportMenu(false);
                        handleExportHTML();
                      }}
                    >
                      <FaFileCode className="w-3 h-3 text-blue-500" />
                      <span>导出HTML</span>
                    </button>

                    <button
                      className="w-full px-3 py-2 text-xs text-left hover:bg-gray-50 flex items-center space-x-2"
                      onClick={() => {
                        setShowExportMenu(false);
                        handleExportImage();
                      }}
                    >
                      <FaFileImage className="w-3 h-3 text-orange-500" />
                      <span>导出图片</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          )
        }

        {/* ========== 内容区域 ========== */}
        <div className="flex-1 overflow-hidden">
          {!htmlContent ? (
            // 没有内容时的占位符
            <div className="w-full h-full flex items-center justify-center bg-gray-50">
              <div className="text-center text-gray-500">
                <div className="text-lg mb-2">暂无报告内容</div>
                <div className="text-sm">报告生成完成后将在此显示</div>
              </div>
            </div>
          ) : (
            <div className="relative w-full h-full">
              {/* ========== 报告模式 ========== */}
              {/* {viewMode === 'report' && (
                <div
                  ref={htmlContainerRef}
                  className="w-full h-full overflow-auto p-4 bg-gray-50"
                >
                  <div className="bg-white rounded-lg shadow-sm">
                    <div className="p-4">
                      <SimpleMarkdown content={htmlContent} />
                    </div>
                  </div>
                </div>
              )} */}

              {/* ========== 预览模式 ========== */}
              {viewMode === 'preview' && (
                <div className="w-full h-full relative">
                  {/* Loading 遮罩 */}
                  {isIframeLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white z-20">
                      <div className="flex flex-col items-center space-y-3">
                        <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <div className="text-sm text-gray-500">正在加载预览...</div>
                      </div>
                    </div>
                  )}

                  {/* iframe 用于预览 HTML 内容 */}
                  <iframe
                    className="w-full h-full border-0"
                    title="报告内容"
                    srcDoc={parsedHtml}
                    sandbox="allow-scripts allow-same-origin allow-popups-to-escape-sandbox"
                    onLoad={handleIframeLoad}
                    referrerPolicy="no-referrer"
                    loading="eager"
                  />
                </div>
              )}

              {/* ========== 代码模式 ========== */}
              {viewMode === 'code' && (
                <div
                  ref={htmlContainerRef}
                  className="w-full h-full overflow-auto p-4 bg-gray-50"
                >
                  <div className="bg-white rounded-lg shadow-sm">
                    <div className="p-3 border-b bg-gray-100 text-sm font-medium text-gray-700">
                      HTML源代码
                    </div>
                    <SimpleHtmlMarkdown htmlContent={htmlContent} />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};