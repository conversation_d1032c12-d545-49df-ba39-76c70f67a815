import React from 'react';
import { Collapse, Card, Tag, Row, Col } from 'antd';
import {
  BulbOutlined,
  SolutionOutlined,
} from '@ant-design/icons';

interface Props {
  stageData: any;
}

const AnalysisStageContent: React.FC<Props> = ({ stageData }) => {
  if (!stageData) return <Card>无分析数据</Card>;

  const deep = stageData.deep_analysis_result;
  if (!deep) return <Card>无分析结果</Card>;

  const insights = deep.analysis_findings?.key_insights || [];
  const recommendations = deep.recommendations || [];

  /* ---------- 颜色工具函数 ---------- */
  const confidenceColor = (c: string) =>
    ({ 高: 'success', 中: 'warning', 低: 'default' }[c] || 'default');
  const priorityColor = (p: string) =>
    ({ 高: 'error', 中: 'warning', 低: 'default' }[p] || 'default');
  const goalColor = (g: string) =>
    ({ 完全达成: 'success', 基本达成: 'warning', 未达成: 'error' }[g] || 'default');

  /* ---------- 新写法：items 数组 ---------- */
  const items = [
    {
      key: 'insights',
      label: (
        <span className="flex items-center gap-2">
          <BulbOutlined className="text-orange-500" />
          关键洞察 ({insights.length})
        </span>
      ),
      children: (
        <Row gutter={[16, 16]}>
          {insights.map((ins: any, idx: number) => (
            <Col xs={24} md={12} key={idx}>
              <Card size="small" hoverable>
                <div className="text-sm mb-2">{ins.insight}</div>
                {ins.confidence && (
                  <Tag color={confidenceColor(ins.confidence)}>
                    {ins.confidence}置信
                  </Tag>
                )}
                {[...(ins.supporting_data || []), ...(ins.supporting_evidence || [])]
                  .length > 0 && (
                    <ul className="list-disc pl-4 mt-2 text-xs text-gray-600">
                      {[...ins.supporting_data, ...ins.supporting_evidence].map(
                        (d: string, i: number) => (
                          <li key={i}>{d}</li>
                        )
                      )}
                    </ul>
                  )}
              </Card>
            </Col>
          ))}
        </Row>
      ),
    },
    {
      key: 'recommendations',
      label: (
        <span className="flex items-center gap-2">
          <SolutionOutlined className="text-blue-500" />
          建议措施 ({recommendations.length})
        </span>
      ),
      children: (
        <Row gutter={[16, 16]}>
          {recommendations.map((rec: any, idx: number) => (
            <Col xs={24} md={12} key={idx}>
              <Card size="small" hoverable>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-sm">
                    {rec.type || '建议'}
                  </span>
                  {rec.priority && (
                    <Tag color={priorityColor(rec.priority)}>
                      {rec.priority}
                    </Tag>
                  )}
                </div>
                <p className="text-sm mb-2">{rec.action}</p>
                {rec.expected_impact && (
                  <div className="text-xs text-gray-500">
                    预期：{rec.expected_impact}
                  </div>
                )}
                {rec.timeline && (
                  <div className="text-xs text-gray-500">
                    时间：{rec.timeline}
                  </div>
                )}
              </Card>
            </Col>
          ))}
        </Row>
      ),
    },
  ];

  return (
    <div className="pr-2">
      <Collapse
        bordered={false}
        defaultActiveKey={['insights', 'recommendations', 'status']}
        className="bg-white"
        items={items} /* ← 新 API */
      />

      {/* 底部统计 */}
      <Card size="small" className="mt-4">
        <Row gutter={16} className="text-center">
          <Col span={12}>
            <div className="text-gray-500">洞察数量</div>
            <div className="text-xl font-semibold">{insights.length}</div>
          </Col>
          <Col span={12}>
            <div className="text-gray-500">建议数量</div>
            <div className="text-xl font-semibold">{recommendations.length}</div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default AnalysisStageContent;