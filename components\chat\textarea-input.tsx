import React, { useState } from 'react';
import { Textarea } from '../ui/textarea';
import { IoSendSharp } from "react-icons/io5";
import { IoIosArrowDown } from 'react-icons/io';
import { FaStopCircle } from "react-icons/fa";
import { cn } from '@/lib/utils';
import { useConversionStore } from '@/store/useConversion';
import { shallow } from 'zustand/shallow';
import classnames from 'classnames';

interface TextareaInputProps {
  scrollToBottom: () => void;
  isBottom: boolean;
}

const TextareaInput = ({ scrollToBottom, isBottom }: TextareaInputProps) => {
  const { handleSendMessage, isResponding, stopChatMessage, chatType, setChatType } = useConversionStore((state) => ({
    handleSendMessage: state.handleSendMessage,
    isResponding: state.isResponding,
    stopChatMessage: state.stopChatMessage,
    chatType: state.chatType,
    setChatType: state.setChatType,
  }), shallow)

  const [query, setQuery] = useState('')



  // 回车发送
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const newQuery = query.replace(/\n/g, '').trim();
    if (!newQuery.length || isResponding) return
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(query)
      clearQuery()
      scrollToBottom()
    }
  }

  const clearQuery = () => {
    setQuery('')
  }

  const handleClick = () => {
    const newQuery = query.replace(/\n/g, '').trim();
    if (!newQuery.length || isResponding) return
    handleSendMessage(query)
    clearQuery()
    scrollToBottom()
  }

  return (
    <div className='sticky bottom-0 lg:pb-6 pb-4 px-4 left-0 min-h-[140px] max-h-[180px] w-full bg-white'>
      <div
        onClick={scrollToBottom}
        className={cn(
          'absolute cursor-pointer left-[50%] -top-14 p-2 bg-white rounded-full shadow-lg isolate',
          'transition-all duration-300 ease-in-out', // 添加过渡动画
          {
            'opacity-0 invisible': isBottom,  // 替换 hidden
            'opacity-100 visible': !isBottom // 替换 block
          }
        )}
      >
        <div className={classnames('absolute inset-0 rounded-[1.5rem]', {
          'border-2 border-t-transparent border-r-transparent border-b-transparent border-blue-300 animate-spin': isResponding
        })}></div>
        <IoIosArrowDown className='text-xl text-blue-300 relative z-10' />
      </div>
      <div className='lg:w-[790px] flex border border-[#EBEBEB] flex-col max-w-full w-full mx-auto rounded-2xl bg-[#F3F4F6] h-full'>
        <Textarea
          className='outline-none border-0 shadow-none p-4 text-sm h-full resize-none focus:outline-none focus-visible:ring-0 focus:border-0'
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <div className='h-10 w-full p-4 flex items-center justify-between'>
          <div className='flex items-center space-x-2'>
            <button
              className={cn(
                'lg:px-3 px-1 py-1 rounded-md lg:text-sm text-xs',
                chatType === 'kb' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700',
                'hover:bg-blue-600 hover:text-white'
              )}
              onClick={() => setChatType(chatType === 'kb' ? 'default' : 'kb')} // 点击按钮切换 isKb 状态
            >
              知识库问答
            </button>
            <button
              className={cn(
                'lg:px-3 px-1 py-1 rounded-md lg:text-sm text-xs',
                chatType === 'web' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700',
                'hover:bg-blue-600 hover:text-white'
              )}
              onClick={() => setChatType(chatType === 'web' ? 'default' : 'web')} // 点击按钮切换 isKb 状态
            >
              联网搜索
            </button>
            <button
              className={cn(
                'lg:px-3 px-1 py-1 rounded-md lg:text-sm text-xs',
                chatType === 'process' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700',
                'hover:bg-blue-600 hover:text-white'
              )}
              onClick={() => setChatType(chatType === 'process' ? 'default' : 'process')} // 点击按钮切换 isKb 状态
            >
              深度问答
            </button>
          </div>
          {
            isResponding ? (
              <FaStopCircle className='text-2xl text-blue-500 cursor-pointer' onClick={() => stopChatMessage()} />
            ) : <IoSendSharp
              onClick={handleClick}
              className={cn('text-2xl text-gray-500 cursor-pointer', { 'text-blue-500': query })}
            />
          }
        </div>
      </div>
    </div>
  );
}

export default TextareaInput;
