import React, { useEffect, useMemo, useState } from 'react';
import { useConversionStore } from '@/store/useConversion';
import { Textarea } from '../ui/textarea';
import { IoSendSharp } from "react-icons/io5";
import { shallow } from 'zustand/shallow';
import { cn } from '@/lib/utils';
import QuestionArea from './quesiton-area';
import { GoSidebarCollapse } from 'react-icons/go';
import { useTranslation } from 'react-i18next';
import { SelectorQuestion } from './selector-question';
import DataSetContext, { DataSet } from '@/context/data-set';
import { fetchDataSetList, fetchSuggestionByDataset } from '@/services/api';
import CategoryQuestionArea from '../categroy-qestion';
import CategoryPanel from '../categroy-qestion';

const Welcome = () => {
  const [query, setQuery] = useState('');
  const { t } = useTranslation()
  const [dataSet, setDataSet] = useState<DataSet[]>([])
  const [currentDataset, setCurrentDataset] = useState<DataSet>({ dataset_id: 0, dataset_name: '智能选择' })
  const [questionList, setQuestionList] = useState<{ id: number, question: string }[]>([])
  const [mapDataSetPage, setMapDataSetPage] = useState<Record<string, any>>({})
  const [currentPage, setCurrentPage] = useState(1)

  const { handleSendMessage, startChat, isResponding, changeCollapsed, isCollapsed, isMobileCollapsed, setIsMobileCollapsed, chatType } = useConversionStore((state) => ({
    handleSendMessage: state.handleSendMessage,
    startChat: state.startChat,
    isResponding: state.isResponding,
    changeCollapsed: state.changeCollapsed,
    isCollapsed: state.isCollapsed,
    isMobileCollapsed: state.isMobileCollapsed,
    setIsMobileCollapsed: state.setIsMobileCollapsed,
    chatType: state.chatType,
  }), shallow);

  // 根据当前时间来生成早上好，中午好，下午好，晚上好
  const getGreeting = useMemo(() => {
    const hour = new Date().getHours();
    if (hour < 12) {
      return t('common.welcome.goodMorning');
    } else if (hour < 14) {
      return t('common.welcome.goodNoon');
    } else if (hour < 18) {
      return t('common.welcome.goodAfternoon');
    } else if (hour < 24) {
      return t('common.welcome.goodEvening');
    }
  }, [])

  // 回车发送
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const newQuery = query.replace(/\n/g, '').trim();
    if (!newQuery.length || isResponding) return
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // 在这里处理发送消息的逻辑
      startChat()
      handleSendMessage(query)
    }
  }

  const handleClick = () => {
    const newQuery = query.replace(/\n/g, '').trim();
    if (!newQuery.length || isResponding) return
    // 在这里处理点击事件的逻辑
    startChat()
    handleSendMessage(query)
  }

  const onDoubleClick = (text: string) => {
    startChat()
    handleSendMessage(query)
  }

  const getDatasetList = async () => {
    let res = await fetchDataSetList() as any
    // 不管是不是数组，都加上智能选择
    Array.isArray(res) ? res : res = []
    res.unshift({ dataset_id: 0, dataset_name: '智能选择' })
    setDataSet(res)
    if (res && res.length > 0) {
      const mapData: any = {}
      res.forEach((item: DataSet) => {
        mapData[item.dataset_id] = {
          page: 1,
          total: 0,
          total_pages: 0,
        }
      })
      setMapDataSetPage(mapData)
    }
  }

  const handleRefresh = () => {
    if (!currentDataset) return

    const currentPageInfo = mapDataSetPage[currentDataset.dataset_id]
    const totalPages = currentPageInfo.total_pages

    let newPage = currentPageInfo.page

    if (currentPageInfo.page >= totalPages) {
      newPage = 1
    } else {
      newPage = currentPageInfo.page + 1
    }

    // 只更新页码状态
    setMapDataSetPage(prev => ({
      ...prev,
      [currentDataset.dataset_id]: {
        ...prev[currentDataset.dataset_id],
        page: newPage
      }
    }))
    setCurrentPage(newPage) // 触发useEffect
  }

  const changeDataset = (dataset_id: number) => {
    const currentDataset = dataSet.find(item => item.dataset_id === dataset_id)
    if (currentDataset) setCurrentDataset(currentDataset)
  }

  const getQuestionByDatasetId = async (dataset_id: number) => {
    const params = {
      dataset_id: dataset_id,
      page: mapDataSetPage[dataset_id].page,
      limit: 4
    }
    const res = await fetchSuggestionByDataset(params) as any
    setQuestionList(res.data)
    // 更新总条数
    setMapDataSetPage(prev => ({
      ...prev,
      [dataset_id]: {
        ...prev[dataset_id],
        total: res.total,
        total_pages: res.total_pages,
      }
    }))
  }

  useEffect(() => {
    getDatasetList()
  }, [])

  useEffect(() => {
    if (currentDataset.dataset_id) {
      getQuestionByDatasetId(currentDataset.dataset_id)
    } else {
      setQuestionList([
        {
          id: 1,
          question: "哪些账户最近6个月没有交易活动?",
        },
        {
          id: 2,
          question: "最近6个月有多少员工离职?",
        },
        {
          id: 3,
          question: "哪些客户拥有多个账户?",
        },
        {
          id: 4,
          question: "各部门薪资最高的员工是谁?",
        },
      ])
    }
  }, [currentPage, currentDataset.dataset_id])

  return (
    <DataSetContext.Provider value={{ datasetList: dataSet, currentDataset, changeDataset, questionList, refreshQuestionList: handleRefresh }}>
      <div className='w-full h-svh flex flex-col items-center justify-center p-4 relative overflow-auto'>
        <div className={cn('hidden lg:block w-fit mr-2 absolute left-[2%] top-[2%]', { 'lg:hidden': !isCollapsed })}>
          <GoSidebarCollapse className='text-xl cursor-pointer' onClick={() => changeCollapsed()} />
        </div>
        <div className={cn('block lg:hidden w-fit mr-2 absolute left-[2%] top-[2%]', { 'lg:block': !isMobileCollapsed })}>
          <GoSidebarCollapse className='text-xl cursor-pointer' onClick={() => setIsMobileCollapsed(!isMobileCollapsed)} />
        </div>
        <div className='lg:w-[794px] lg:mt-[7.5rem] max-w-full w-full space-y-6 h-full'>
          <div className='lg:text-2xl text-lg lg:mb-4 mb-2 font-[600] text-center'>
            {getGreeting}，{t('common.welcome.title')}
          </div>
          <div className='h-[150px]  w-full z-10 bg-white'>
            <div className='lg:w-[794px] flex border border-[#EBEBEB] flex-col max-w-full w-full mx-auto rounded-2xl bg-white shadow-lg h-full'>
              <Textarea
                className='outline-none border-0 shadow-none p-4 text-sm h-full resize-none focus:outline-none focus-visible:ring-0 focus:border-0'
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
              />
              <div className='h-10 w-full lg:p-4 p-2 flex items-center justify-between'>
                <div className='w-fit'>
                  <SelectorQuestion />
                </div>
                <IoSendSharp
                  onClick={handleClick}
                  className={cn('lg:text-2xl text-xl text-gray-500 cursor-pointer', { 'text-blue-500': query })}
                />
              </div>
            </div>
          </div>
          {/* <QuestionArea onClick={(text) => setQuery(text)} onDoubleClick={onDoubleClick} /> */}
          <CategoryPanel onClick={(text) => setQuery(text)} onDoubleClick={onDoubleClick} />
        </div>
      </div>
    </DataSetContext.Provider>

  );
};

export default Welcome;