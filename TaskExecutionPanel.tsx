"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { <PERSON>, Card<PERSON>eader, CardT<PERSON>le, CardContent, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Head<PERSON>, <PERSON>alogTit<PERSON>, <PERSON>alogTrigger } from "@/components/ui/dialog"
import { TableDisplay } from "@/components/data-display/TableDisplay"

export type ResearchStage = "planning" | "rewriting" | "execution" | "analysis" | string

export interface TaskRecord {
  conversation_id: string
  message_id: string
  task_id: string
  research_result: any
}

export interface TaskExecutionPanelProps {
  data: Record<string, TaskRecord>
  defaultStage?: ResearchStage
}

function groupByStage(data: Record<string, TaskRecord>) {
  const stages: Record<string, TaskRecord[]> = {}
  Object.values(data || {}).forEach((item) => {
    const stage = item?.research_result?.stage || "unknown"
    if (!stages[stage]) stages[stage] = []
    stages[stage].push(item)
  })
  // 每个阶段按 thought_number 升序
  Object.keys(stages).forEach((k) => {
    stages[k].sort(
      (a, b) => (a?.research_result?.thought_number ?? 0) - (b?.research_result?.thought_number ?? 0)
    )
  })
  return stages
}

export default function TaskExecutionPanel({ data, defaultStage = "planning" }: TaskExecutionPanelProps) {
  const grouped = React.useMemo(() => groupByStage(data), [data])
  const stagesToShow: ResearchStage[] = ["planning", "rewriting", "execution", "analysis"].filter(
    (s) => grouped[s] && grouped[s].length > 0
  ) as ResearchStage[]

  // 如果传入的数据没有标准阶段，兜底展示所有存在的阶段
  const tabs = stagesToShow.length > 0 ? stagesToShow : (Object.keys(grouped) as ResearchStage[])

  const initialStage = tabs.includes(defaultStage) ? defaultStage : tabs[0]
  const [activeStage, setActiveStage] = React.useState<ResearchStage>(initialStage)
  const [index, setIndex] = React.useState<number>(0)

  // 当阶段切换时，重置面板索引
  const onStageChange = (value: string) => {
    setActiveStage(value)
    setIndex(0)
  }

  const items = grouped[activeStage] || []
  const total = items.length
  const canPrev = index > 0
  const canNext = index < Math.max(0, total - 1)

  const goPrev = () => {
    if (canPrev) setIndex((v) => Math.max(0, v - 1))
  }
  const goNext = () => {
    if (canNext) setIndex((v) => Math.min(total - 1, v + 1))
  }
  const goStageOffset = (delta: number) => {
    const curIdx = tabs.findIndex((t) => t === activeStage)
    const nextIdx = curIdx + delta
    if (nextIdx >= 0 && nextIdx < tabs.length) {
      setActiveStage(tabs[nextIdx])
      setIndex(0)
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <Tabs value={activeStage} onValueChange={onStageChange}>
        <TabsList>
          {tabs.map((stage) => (
            <TabsTrigger key={stage} value={stage} className="capitalize">
              {stage}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((stage) => (
          <TabsContent key={stage} value={stage}>
            <StageSection
              stage={stage}
              items={grouped[stage] || []}
              activeIndex={stage === activeStage ? index : 0}
              onChangeIndex={setIndex}
              onPrev={goPrev}
              onNext={goNext}
              onNextStage={() => goStageOffset(1)}
              onPrevStage={() => goStageOffset(-1)}
              isActive={stage === activeStage}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}

function StageSection({
  stage,
  items,
  activeIndex,
  onChangeIndex,
  onPrev,
  onNext,
  onNextStage,
  onPrevStage,
  isActive,
}: {
  stage: ResearchStage
  items: TaskRecord[]
  activeIndex: number
  onChangeIndex: (v: number) => void
  onPrev: () => void
  onNext: () => void
  onNextStage: () => void
  onPrevStage: () => void
  isActive: boolean
}) {
  const total = items.length
  const indexSafe = Math.min(Math.max(0, activeIndex), Math.max(0, total - 1))
  const item = items[indexSafe]
  const canPrev = indexSafe > 0
  const canNext = indexSafe < Math.max(0, total - 1)

  return (
    <div className="flex flex-col gap-4">

      {item ? (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">思考序号 #{item?.research_result?.thought_number ?? "-"}</CardTitle>
          </CardHeader>
          <CardContent>
            {stage === "planning" && <PlanningContent rr={item.research_result} />}
            {stage === "rewriting" && <RewritingContent rr={item.research_result} />}
            {stage === "execution" && <ExecutionContent rr={item.research_result} />}
            {stage === "analysis" && <AnalysisContent rr={item.research_result} />}
          </CardContent>
        </Card>
      ) : (
        <div className="text-sm text-muted-foreground">暂无数据</div>
      )}

      {/* 底部导航条 */}
      {isActive && total > 0 && (
        <div className="sticky bottom-0 z-10 mt-2 rounded-xl border bg-background/95 p-3 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
            {/* 进度指示 */}
            <div className="flex items-center gap-2">
              {Array.from({ length: total }).map((_, i) => (
                <button
                  key={i}
                  aria-label={`跳转到第 ${i + 1} 面板`}
                  onClick={() => onChangeIndex(i)}
                  className={`h-8 w-8 rounded-full border text-sm transition-colors ${
                    i === indexSafe ? "bg-primary text-primary-foreground" : "bg-background hover:bg-muted"
                  }`}
                >
                  {i + 1}
                </button>
              ))}
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-2">
              <Button variant="secondary" onClick={onPrev} disabled={!canPrev}>
                ← 上一面板
              </Button>
              <Button onClick={onNext} disabled={!canNext}>
                下一面板 →
              </Button>
              <div className="mx-1 h-6 w-px bg-border" />
              <Button variant="default" onClick={onNextStage}>
                下一阶段 →
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

function SectionTitle({ children }: { children: React.ReactNode }) {
  return <div className="mb-2 text-sm font-semibold text-foreground/90">{children}</div>
}

function RewritingContent({ rr }: { rr: any }) {
  const tools: any[] = rr?.required_tools || []
  const notes: string | undefined = rr?.rewriting_notes || rr?.notes

  if ((!tools || tools.length === 0) && !notes) {
    return <div className="text-sm text-muted-foreground">无重写计划数据</div>
  }

  return (
    <div className="space-y-4">
      {notes && (
        <div>
          <SectionTitle>重写意图</SectionTitle>
          <div className="rounded-lg border bg-background p-3 text-sm whitespace-pre-wrap">{notes}</div>
        </div>
      )}

      {tools && tools.length > 0 && (
        <div>
          <SectionTitle>所需工具与参数</SectionTitle>
          <div className="space-y-3 max-h-[420px] overflow-auto pr-1">
            {tools.map((t, i) => (
              <div key={i} className="rounded-lg border p-3 text-sm">
                <div className="font-medium">{t.tool_name || "未命名工具"}</div>
                {t.parameters && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    {typeof t.parameters?.query === "string" ? (
                      <div className="whitespace-pre-wrap">{t.parameters.query}</div>
                    ) : (
                      <pre className="whitespace-pre-wrap">{JSON.stringify(t.parameters, null, 2)}</pre>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

function ListBullets({ items }: { items?: string[] }) {
  if (!items || items.length === 0) return null
  return (
    <ul className="list-disc space-y-1 pl-5 text-sm">
      {items.map((t, i) => (
        <li key={i}>{t}</li>
      ))}
    </ul>
  )
}

function KeyValueTable({ row }: { row: Record<string, any> }) {
  const entries = Object.entries(row)
  return (
    <div className="overflow-x-auto">
      <table className="w-full min-w-[400px] table-auto border-collapse text-sm">
        <tbody>
          {entries.map(([k, v], i) => (
            <tr key={i} className="border-b last:border-b-0">
              <td className="w-40 whitespace-nowrap bg-muted/40 px-2 py-1 align-top font-medium">{k}</td>
              <td className="px-2 py-1 align-top">{String(v)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function PlanningContent({ rr }: { rr: any }) {
  const plan = rr?.research_plan
  if (!plan) return <div className="text-sm text-muted-foreground">无规划数据</div>
  return (
    <div className="space-y-4">
      {plan?.overall_goal && (
        <div>
          <SectionTitle>总体目标</SectionTitle>
          <div className="rounded-lg border bg-background p-3 text-sm">{plan.overall_goal}</div>
        </div>
      )}

      {plan?.tool_specific_tasks?.nl2sql && (
        <div>
          <SectionTitle>NL2SQL 关注点</SectionTitle>
          <div className="grid gap-3 md:grid-cols-2">
            <div>
              <div className="mb-1 text-xs text-muted-foreground">关键业务指标</div>
              <ListBullets items={plan.tool_specific_tasks.nl2sql.key_business_metrics} />
            </div>
            <div>
              <div className="mb-1 text-xs text-muted-foreground">分析维度</div>
              <ListBullets items={plan.tool_specific_tasks.nl2sql.analysis_dimensions} />
            </div>
          </div>
        </div>
      )}

      {plan?.tool_specific_tasks?.query_knowledge_base && (
        <div>
          <SectionTitle>知识库检索 关注点</SectionTitle>
          <div className="grid gap-3 md:grid-cols-2">
            <div>
              <div className="mb-1 text-xs text-muted-foreground">检索主题</div>
              <ListBullets items={plan.tool_specific_tasks.query_knowledge_base.search_topics} />
            </div>
            <div>
              <div className="mb-1 text-xs text-muted-foreground">重点关注</div>
              <ListBullets items={plan.tool_specific_tasks.query_knowledge_base.focus_areas} />
            </div>
          </div>
        </div>
      )}

      {plan?.tool_specific_tasks?.search_web && (
        <div>
          <SectionTitle>外网检索 关注点</SectionTitle>
          <div className="grid gap-3 md:grid-cols-2">
            <div>
              <div className="mb-1 text-xs text-muted-foreground">研究领域</div>
              <ListBullets items={plan.tool_specific_tasks.search_web.research_areas} />
            </div>
            <div>
              <div className="mb-1 text-xs text-muted-foreground">关键问题</div>
              <ListBullets items={plan.tool_specific_tasks.search_web.key_questions} />
            </div>
          </div>
        </div>
      )}

      {plan?.expected_deliverables && (
        <div>
          <SectionTitle>预期交付</SectionTitle>
          <div className="grid gap-3 md:grid-cols-3">
            <div>
              <div className="mb-1 text-xs text-muted-foreground">关键洞察</div>
              <ListBullets items={plan.expected_deliverables.key_insights} />
            </div>
            <div>
              <div className="mb-1 text-xs text-muted-foreground">建议类型</div>
              <ListBullets items={plan.expected_deliverables.recommendation_types} />
            </div>
            <div>
              <div className="mb-1 text-xs text-muted-foreground">风险识别</div>
              <ListBullets items={plan.expected_deliverables.risk_identifications} />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

function ExecutionContent({ rr }: { rr: any }) {
  const execs: any[] = rr?.tool_executions || []
  if (!execs || execs.length === 0) return <div className="text-sm text-muted-foreground">无执行记录</div>

  return (
    <div className="space-y-4">
      {execs.map((ex, i) => {
        const isNL2SQL = (ex?.tool_name || "").toLowerCase() === "nl2sql"
        const rows: any[] = ex?.result?.retrieval_result || []
        const count = Array.isArray(rows) ? rows.length : 0

        return (
          <div key={i} className="rounded-lg border p-3">
            <div className="mb-1 flex items-center justify-between gap-2">
              <div className="text-sm font-medium">{ex.tool_name || "未知工具"}</div>
              {isNL2SQL && count > 0 && (
                <div className="text-xs text-muted-foreground">共 {count} 条结果</div>
              )}
            </div>

            {/* 问题文案作为触发器 */}
            {ex.query ? (
              <Dialog>
                <DialogTrigger asChild>
                  <button className="mb-2 w-full rounded-md border bg-muted/40 p-2 text-left text-xs text-muted-foreground hover:bg-muted/60">
                    {ex.query}
                  </button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-5xl">
                  <DialogHeader>
                    <DialogTitle>执行结果 · {ex.tool_name}</DialogTitle>
                  </DialogHeader>
                  {isNL2SQL && Array.isArray(rows) && rows.length > 0 ? (
                    <div className="max-h-[70vh] overflow-auto">
                      <TableDisplay data={rows} />
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">无结构化结果</div>
                  )}
                </DialogContent>
              </Dialog>
            ) : (
              <div className="text-xs text-muted-foreground">无查询问题</div>
            )}
          </div>
        )
      })}
    </div>
  )
}

function AnalysisContent({ rr }: { rr: any }) {
  const deep = rr?.deep_analysis_result
  if (!deep)
    return <div className="text-sm text-muted-foreground">无分析结果</div>

  const insights: any[] = deep?.analysis_findings?.key_insights || []
  const recs: any[] = deep?.recommendations || []

  return (
    <div className="space-y-4">
      <div>
        <SectionTitle>关键洞察</SectionTitle>
        {insights.length === 0 ? (
          <div className="text-sm text-muted-foreground">暂无</div>
        ) : (
          <div className="space-y-3">
            {insights.map((ins, i) => (
              <div key={i} className="rounded-lg border p-3 text-sm">
                <div className="font-medium">{ins.insight}</div>
                <div className="mt-1 text-xs text-muted-foreground">可信度：{ins.confidence || "-"}</div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div>
        <SectionTitle>建议措施</SectionTitle>
        {recs.length === 0 ? (
          <div className="text-sm text-muted-foreground">暂无</div>
        ) : (
          <div className="grid gap-3 md:grid-cols-2">
            {recs.map((r, i) => (
              <div key={i} className="rounded-lg border p-3 text-sm">
                <div className="font-medium">{r.type}</div>
                <div className="mt-1">{r.action}</div>
                {r.priority && (
                  <div className="mt-2 inline-flex rounded-md border px-2 py-0.5 text-xs text-muted-foreground">
                    优先级：{r.priority}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
