import { streamSSEReport } from '@/services/handlerReport';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export interface ReportData {
  msgId?: string;
  title: string;
  htmlContent?: string; // 注意：这里通常是字符串，但下面的实现也兼容 undefined
}

interface ReportStore {
  currentReport: ReportData | null;
  // 正在更新（流式生成）中的消息ID
  updatingMessageId: string | null;
  // 存放各条消息的最终报告，避免不同消息之间相互覆盖
  reportsByMessageId: Record<string, ReportData>;
  // 报告流式加载状态
  isReportLoading: boolean;
  setCurrentReport: (report: ReportData) => void;
  clearCurrentReport: () => void;
  fetchFinalReport: (messageId: string) => void;
  // 存储与获取最终报告（供面板查询）
  setReportForMessage: (messageId: string, report: ReportData) => void;
  getReportByMessageId: (messageId: string) => ReportData | null;
}

export const useReportStore = create(
  subscribeWithSelector<ReportStore>((set, get) => ({
    currentReport: null,
    updatingMessageId: null,
    reportsByMessageId: {},
    isReportLoading: false,
    setCurrentReport: (report) => {
      set({ currentReport: report });
    },
    clearCurrentReport: () => set({ currentReport: null }),

    fetchFinalReport: async (messageId: string) => {
      // 设定当前处于更新状态的消息ID，并清空/初始化 currentReport
      set((state) => ({
        updatingMessageId: messageId,
        isReportLoading: true,
        currentReport: {
          msgId: messageId,
          title: '分析报告',
          htmlContent: '',
        },
      }));

      try {
        const abortController = await streamSSEReport(
          `${process.env.NEXT_PUBLIC_API_URL}/api/chat/generateHtmlReport?message_id=${messageId}`,
          {},
          {
            onMessage: (fullContent: string) => {
              // 这里的 fullContent 已是服务端流式累积后的完整片段（handlerReport.ts 中实现）
              // 直接覆盖 currentReport.htmlContent，确保面板总是显示最新完整内容
              set((state) => ({
                currentReport: {
                  msgId: messageId,
                  title: '分析报告',
                  htmlContent: fullContent,
                },
                updatingMessageId: messageId,
              }));
            },
            onError: (error) => {
              console.error('fetchFinalReport onError:', error);
              set({ isReportLoading: false });
            },
            onFinish: (finalHtml: string) => {
              // 结束时，将最终报告写入 reportsByMessageId，供其他消息查看时读取
              set((state) => ({
                reportsByMessageId: {
                  ...state.reportsByMessageId,
                  [messageId]: {
                    msgId: messageId,
                    title: '分析报告',
                    htmlContent: finalHtml,
                  },
                },
                // 流式结束后仍然保留 currentReport 以供当前面板显示
                updatingMessageId: null,
                isReportLoading: false,
              }));
            },
          }
        );

        // 可选：返回的 abortController 供上层在需要时中断
        // 暂不在此 store 内持有；由调用方自行管理
        void abortController;
      } catch (e) {
        console.error('fetchFinalReport failed:', e);
        set({ isReportLoading: false });
      }
    },
    setReportForMessage: (messageId, report) =>
      set((state) => ({
        reportsByMessageId: {
          ...state.reportsByMessageId,
          [messageId]: report,
        },
      })),
    getReportByMessageId: (messageId) => {
      const { reportsByMessageId } = get();
      return reportsByMessageId[messageId] || null;
    },
  }))
);
